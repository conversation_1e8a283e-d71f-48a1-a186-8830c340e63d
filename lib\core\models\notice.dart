class Notice {
  final String id;
  final String title;
  final String content;
  final DateTime datePublished;
  final String? attachmentUrl; // Optional
  // Add other relevant notice details: author, category, etc.

  const Notice({
    required this.id,
    required this.title,
    required this.content,
    required this.datePublished,
    this.attachmentUrl,
  });

  // Example: factory Notice.fromMap(Map<String, dynamic> map) { ... }
  // Example: Map<String, dynamic> toMap() { ... }
}