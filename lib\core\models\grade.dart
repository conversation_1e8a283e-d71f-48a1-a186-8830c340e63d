class Grade {
  final String semesterName;
  final String courseCode;
  final String courseName; // Title of Course
  final double creditHours;
  final String gradeValue; // Letter grade, e.g., "A+", "B-"
  final double? gpaPoints;  // GPA points for this course, might be calculated
  // Add other relevant grade details: exam type, etc.

  const Grade({
    required this.semesterName,
    required this.courseCode,
    required this.courseName,
    required this.creditHours,
    required this.gradeValue,
    this.gpaPoints, // Made nullable as it's not directly in the table per course
  });

  // Example: factory Grade.fromMap(Map<String, dynamic> map) { ... }
  // Example: Map<String, dynamic> toMap() { ... }
}