import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:iubat_buddy/core/models/notice.dart';
import 'package:iubat_buddy/core/repositories/iubat_repository.dart';
import 'package:meta/meta.dart';

part 'notices_event.dart';
part 'notices_state.dart';

class NoticesBloc extends Bloc<NoticesEvent, NoticesState> {
  final IubatRepository iubatRepository;

  NoticesBloc({required this.iubatRepository}) : super(NoticesInitial()) {
    on<LoadNotices>(_onLoadNotices);
  }

  Future<void> _onLoadNotices(LoadNotices event, Emitter<NoticesState> emit) async {
    emit(NoticesLoading());
    try {
      // TODO: Remove placeholder data and use repository
      // final notices = await iubatRepository.getNotices();
      // emit(NoticesLoaded(notices));

      // Placeholder data for now
      await Future.delayed(const Duration(seconds: 1)); // Simulate network delay
      final List<Notice> placeholderNotices = [
        Notice(id: 'N001', title: 'Midterm Exam Schedule Fall 2024', content: 'The midterm examination schedule for Fall 2024 has been published. Please check the notice board and university website.', datePublished: DateTime(2024, 10, 15), attachmentUrl: 'https://example.com/midterm_schedule.pdf'),
        Notice(id: 'N002', title: 'Holiday Announcement: Victory Day', content: 'The university will remain closed on December 16th on account of Victory Day.', datePublished: DateTime(2024, 12, 10), attachmentUrl: ''),
        Notice(id: 'N003', title: 'Call for Papers: Annual Tech Symposium', content: 'Submit your research papers for the Annual Tech Symposium by November 30th. Visit the CSE department website for more details.', datePublished: DateTime(2024, 10, 1), attachmentUrl: 'https://example.com/symposium_cfp.pdf'),
      ];
      emit(NoticesLoaded(placeholderNotices));

    } catch (e) {
      emit(NoticesError(e.toString()));
    }
  }
}