import 'package:flutter/material.dart';
import 'package:iubat_buddy/core/models/grade.dart'; // Assuming Grade model

class GradeItem extends StatelessWidget {
  final Grade grade;

  const GradeItem({
    super.key,
    required this.grade,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2.0,
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    grade.courseName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Code: ${grade.courseCode}', // Changed from courseId to courseCode
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Colors.grey[700]),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    'Credits: ${grade.creditHours.toStringAsFixed(1)}', // Display credit hours
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Colors.grey[700]),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: <Widget>[
                Text(
                  grade.gradeValue, // Letter Grade
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold, color: _getGradeColor(context, grade.gpaPoints ?? 0.0)), // Use 0.0 if gpaPoints is null for color
                ),
                if (grade.gpaPoints != null) ...[ // Only show GPA if available
                  const SizedBox(height: 4),
                  Text(
                    'Points: ${grade.gpaPoints!.toStringAsFixed(2)}', // Use ! because we checked for null
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey[800]),
                  ),
                ]
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getGradeColor(BuildContext context, double gpaPoints) { // gpaPoints is already passed as non-null (or default 0.0)
    if (gpaPoints >= 3.75) return Colors.green.shade700; // Adjusted thresholds slightly to match common scales
    if (gpaPoints >= 3.00) return Colors.lightGreen.shade600;
    if (gpaPoints >= 2.0) return Colors.orange.shade700;
    return Colors.red.shade700;
  }
}