import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:iubat_buddy/core/models/exam_routine.dart';
import 'package:iubat_buddy/features/exam_routine/bloc/exam_routine_bloc.dart';
import 'package:intl/intl.dart';

class ExamRoutineScreen extends StatefulWidget {
  const ExamRoutineScreen({super.key});

  @override
  State<ExamRoutineScreen> createState() => _ExamRoutineScreenState();
}

class _ExamRoutineScreenState extends State<ExamRoutineScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    context.read<ExamRoutineBloc>().add(ExamRoutineLoadRequested());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Exam Routine'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Upcoming', icon: Icon(Icons.schedule)),
            Tab(text: 'All Exams', icon: Icon(Icons.list)),
            Tab(text: 'Calendar', icon: Icon(Icons.calendar_month)),
          ],
        ),
      ),
      body: BlocBuilder<ExamRoutineBloc, ExamRoutineState>(
        builder: (context, state) {
          if (state is ExamRoutineLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is ExamRoutineError) {
            return _buildErrorState(state.message);
          } else if (state is ExamRoutineLoaded) {
            return TabBarView(
              controller: _tabController,
              children: [
                _buildUpcomingTab(state.examRoutine),
                _buildAllExamsTab(state.examRoutine),
                _buildCalendarTab(state.examRoutine),
              ],
            );
          }
          return const Center(child: Text('No exam routine available'));
        },
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load exam routine',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              context.read<ExamRoutineBloc>().add(ExamRoutineLoadRequested());
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildUpcomingTab(ExamRoutine examRoutine) {
    final upcomingExams = examRoutine.upcomingExams;
    
    if (upcomingExams.isEmpty) {
      return _buildEmptyState(
        icon: Icons.event_available,
        title: 'No Upcoming Exams',
        subtitle: 'You have no exams scheduled in the near future.',
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<ExamRoutineBloc>().add(ExamRoutineLoadRequested());
      },
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildStudentInfoCard(examRoutine),
          const SizedBox(height: 16),
          
          if (examRoutine.nextExam != null) ...[
            _buildNextExamCard(examRoutine.nextExam!),
            const SizedBox(height: 16),
          ],
          
          Text(
            'Upcoming Exams',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          
          ...upcomingExams.map((exam) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: _buildExamCard(exam),
          )),
        ],
      ),
    );
  }

  Widget _buildAllExamsTab(ExamRoutine examRoutine) {
    final allExams = examRoutine.examsSortedByDate;
    
    if (allExams.isEmpty) {
      return _buildEmptyState(
        icon: Icons.school,
        title: 'No Exams Found',
        subtitle: 'No exam routine is available for this semester.',
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<ExamRoutineBloc>().add(ExamRoutineLoadRequested());
      },
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildStudentInfoCard(examRoutine),
          const SizedBox(height: 16),
          
          // Regular Exams Section
          if (examRoutine.regularExams.isNotEmpty) ...[
            _buildSectionHeader('Regular Exams', examRoutine.regularExams.length),
            const SizedBox(height: 12),
            ...examRoutine.regularExams.map((exam) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: _buildExamCard(exam),
            )),
            const SizedBox(height: 16),
          ],
          
          // ELCT Exams Section
          if (examRoutine.elctExams.isNotEmpty) ...[
            _buildSectionHeader('ELCT Exams', examRoutine.elctExams.length),
            const SizedBox(height: 12),
            ...examRoutine.elctExams.map((exam) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: _buildExamCard(exam),
            )),
          ],
        ],
      ),
    );
  }

  Widget _buildCalendarTab(ExamRoutine examRoutine) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<ExamRoutineBloc>().add(ExamRoutineLoadRequested());
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStudentInfoCard(examRoutine),
            const SizedBox(height: 16),
            
            Text(
              'Exam Calendar',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Calendar view would go here
            // For now, show a monthly breakdown
            _buildMonthlyBreakdown(examRoutine),
          ],
        ),
      ),
    );
  }

  Widget _buildStudentInfoCard(ExamRoutine examRoutine) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.person,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Student Information',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildInfoRow('Name', examRoutine.studentName),
            _buildInfoRow('Student ID', examRoutine.studentId),
            _buildInfoRow('Program', examRoutine.program),
            _buildInfoRow('Semester', examRoutine.semester),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNextExamCard(ExamItem exam) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              Color(exam.statusColor).withAlpha(100),
              Color(exam.statusColor).withAlpha(50),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.priority_high,
                  color: Color(exam.statusColor),
                ),
                const SizedBox(width: 8),
                Text(
                  'Next Exam',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              exam.courseCode,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Color(exam.statusColor),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${exam.formattedDate} (${exam.dayName})',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Text(
              exam.timeRange,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Color(exam.statusColor),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                exam.statusText,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExamCard(ExamItem exam) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _showExamDetails(exam),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      exam.courseCode,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Color(exam.examType.color).withAlpha(30),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      exam.examType.displayName,
                      style: TextStyle(
                        color: Color(exam.examType.color),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              Row(
                children: [
                  Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${exam.formattedDate} (${exam.dayName})',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
              const SizedBox(height: 4),

              Row(
                children: [
                  Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    exam.timeRange,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
              const SizedBox(height: 4),

              Row(
                children: [
                  Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    exam.roomAndSeat,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),

              if (exam.daysUntilExam >= 0) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Color(exam.statusColor).withAlpha(30),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    exam.statusText,
                    style: TextStyle(
                      color: Color(exam.statusColor),
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, int count) {
    return Row(
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(width: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withAlpha(30),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            count.toString(),
            style: TextStyle(
              color: Theme.of(context).primaryColor,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMonthlyBreakdown(ExamRoutine examRoutine) {
    final examsByMonth = <String, List<ExamItem>>{};

    for (final exam in examRoutine.examsSortedByDate) {
      final monthKey = DateFormat('MMMM yyyy').format(exam.examDate);
      examsByMonth.putIfAbsent(monthKey, () => []).add(exam);
    }

    return Column(
      children: examsByMonth.entries.map((entry) {
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  entry.key,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                ...entry.value.map((exam) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: Color(exam.examType.color),
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          '${exam.examDate.day} - ${exam.courseCode} (${exam.timeRange})',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                )),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  void _showExamDetails(ExamItem exam) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(exam.courseCode),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Date', '${exam.formattedDate} (${exam.dayName})'),
            _buildDetailRow('Time', exam.timeRange),
            _buildDetailRow('Section', exam.section),
            _buildDetailRow('Room & Seat', exam.roomAndSeat),
            _buildDetailRow('Faculty', exam.courseFaculty),
            _buildDetailRow('Type', exam.examType.displayName),
            if (exam.daysUntilExam >= 0)
              _buildDetailRow('Status', exam.statusText),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
