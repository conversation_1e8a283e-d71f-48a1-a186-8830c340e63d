import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class CalendarDaySelector extends StatefulWidget {
  final Function(DateTime) onDateSelected;
  final DateTime initialDate;
  final List<String> daysWithClasses;

  const CalendarDaySelector({
    super.key,
    required this.onDateSelected,
    required this.initialDate,
    required this.daysWithClasses,
  });

  @override
  State<CalendarDaySelector> createState() => _CalendarDaySelectorState();
}

class _CalendarDaySelectorState extends State<CalendarDaySelector> {
  late DateTime _selectedDate;
  late DateTime _currentMonth;
  late List<DateTime> _visibleDays;
  bool _isCalendarExpanded = false;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialDate;
    _currentMonth = DateTime(_selectedDate.year, _selectedDate.month);
    _generateVisibleDays();
  }

  void _generateVisibleDays() {
    if (_isCalendarExpanded) {
      // Generate days for the current month view
      final firstDayOfMonth = DateTime(_currentMonth.year, _currentMonth.month, 1);
      final lastDayOfMonth = DateTime(_currentMonth.year, _currentMonth.month + 1, 0);

      // Start from the Monday of the week containing the 1st of the month
      final startOffset = (firstDayOfMonth.weekday - 1) % 7;
      final startDate = firstDayOfMonth.subtract(Duration(days: startOffset));

      // Calculate how many days to show (enough to include the last day of month)
      final daysToShow = startOffset + lastDayOfMonth.day;
      final rowsNeeded = (daysToShow / 7).ceil();
      final totalDays = rowsNeeded * 7;

      _visibleDays = List.generate(
        totalDays,
        (index) => startDate.add(Duration(days: index))
      );
    } else {
      // Generate days for a single row (current week + some days)
      final today = DateTime.now();
      final startDate = today.subtract(Duration(days: 3)); // 3 days before today
      _visibleDays = List.generate(14, (index) => startDate.add(Duration(days: index)));
    }
  }

  bool _isDayWithClasses(DateTime date) {
    final dayName = DateFormat('EEEE').format(date);
    return widget.daysWithClasses.contains(dayName);
  }

  bool _isCurrentMonth(DateTime date) {
    return date.month == _currentMonth.month && date.year == _currentMonth.year;
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month && date.day == now.day;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Month selector with expand/collapse button
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                icon: const Icon(Icons.chevron_left),
                onPressed: () {
                  setState(() {
                    _currentMonth = DateTime(_currentMonth.year, _currentMonth.month - 1);
                    _generateVisibleDays();
                  });
                },
              ),
              GestureDetector(
                onTap: () {
                  setState(() {
                    _isCalendarExpanded = !_isCalendarExpanded;
                    _generateVisibleDays();
                  });
                },
                child: Row(
                  children: [
                    Text(
                      DateFormat('MMMM yyyy').format(_currentMonth),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Icon(
                      _isCalendarExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                      size: 20,
                    ),
                  ],
                ),
              ),
              IconButton(
                icon: const Icon(Icons.chevron_right),
                onPressed: () {
                  setState(() {
                    _currentMonth = DateTime(_currentMonth.year, _currentMonth.month + 1);
                    _generateVisibleDays();
                  });
                },
              ),
            ],
          ),
        ),

        // Weekday headers (only show in expanded mode)
        if (_isCalendarExpanded)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: List.generate(7, (index) {
                final dayNames = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];
                return SizedBox(
                  width: 40,
                  child: Center(
                    child: Text(
                      dayNames[index],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                );
              }),
            ),
          ),

        // Calendar view (grid or row)
        _isCalendarExpanded
            ? // Expanded calendar grid
              SizedBox(
                height: 220,
                child: GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 7,
                    childAspectRatio: 1.0,
                  ),
                  itemCount: _visibleDays.length,
                  itemBuilder: (context, index) {
                    final day = _visibleDays[index];
                    final isSelected = day.day == _selectedDate.day &&
                                      day.month == _selectedDate.month &&
                                      day.year == _selectedDate.year;
                    final hasClasses = _isDayWithClasses(day);
                    final isToday = _isToday(day);
                    final isCurrentMonth = _isCurrentMonth(day);

                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedDate = day;
                        });
                        widget.onDateSelected(day);
                      },
                      child: Container(
                        margin: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: isSelected ? Colors.black : Colors.transparent,
                          borderRadius: BorderRadius.circular(20),
                          border: isToday && !isSelected ? Border.all(color: Colors.black, width: 2) : null,
                        ),
                        child: Center(
                          child: Text(
                            day.day.toString(),
                            style: TextStyle(
                              color: !isCurrentMonth ? Colors.grey.withAlpha(100) :
                                     isSelected ? Colors.white :
                                     hasClasses ? Colors.black.withAlpha(220) :
                                     Colors.grey,
                              fontWeight: isSelected || isToday || hasClasses ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              )
            : // Single row of dates
              SizedBox(
                height: 60,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _visibleDays.length,
                  itemBuilder: (context, index) {
                    final day = _visibleDays[index];
                    final isSelected = day.day == _selectedDate.day &&
                                      day.month == _selectedDate.month &&
                                      day.year == _selectedDate.year;
                    final hasClasses = _isDayWithClasses(day);
                    final isToday = _isToday(day);

                    // Format day name (e.g., "Mon")
                    final dayName = DateFormat('E').format(day).substring(0, 1);

                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedDate = day;
                        });
                        widget.onDateSelected(day);
                      },
                      child: Container(
                        width: 40,
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              dayName,
                              style: TextStyle(
                                fontSize: 12,
                                color: isSelected ? Colors.black : Colors.grey,
                                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Container(
                              width: 36,
                              height: 36,
                              decoration: BoxDecoration(
                                color: isSelected ? Colors.black : Colors.transparent,
                                shape: BoxShape.circle,
                                border: isToday && !isSelected ? Border.all(color: Colors.black, width: 2) : null,
                              ),
                              child: Center(
                                child: Text(
                                  day.day.toString(),
                                  style: TextStyle(
                                    color: isSelected ? Colors.white : (hasClasses ? Colors.black : Colors.grey),
                                    fontWeight: isSelected || isToday ? FontWeight.bold : FontWeight.normal,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
      ],
    );
  }
}
