import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:iubat_buddy/features/schedule/bloc/schedule_bloc.dart';
import 'package:iubat_buddy/features/schedule/widgets/calendar_day_selector.dart';
import 'package:iubat_buddy/features/schedule/widgets/schedule_timeline.dart';

class ScheduleScreen extends StatefulWidget {
  const ScheduleScreen({super.key});

  @override
  State<ScheduleScreen> createState() => _ScheduleScreenState();
}

class _ScheduleScreenState extends State<ScheduleScreen> {
  late DateTime _selectedDate;
  String _selectedDayName = '';

  @override
  void initState() {
    super.initState();
    _selectedDate = DateTime.now();
    _selectedDayName = DateFormat('EEEE').format(_selectedDate);
    context.read<ScheduleBloc>().add(LoadSchedule());
  }

  void _onDateSelected(DateTime date) {
    setState(() {
      _selectedDate = date;
      _selectedDayName = DateFormat('EEEE').format(date);
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ScheduleBloc, ScheduleState>(
      builder: (context, state) {
        if (state is ScheduleLoaded) {
          // We'll show the selected day even if it has no classes

          return Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              title: const Text('Class Routine'),
              backgroundColor: Colors.white,
              foregroundColor: Colors.black,
              elevation: 0,
            ),
            body: Column(
              children: [
                // Calendar day selector
                Card(
                  margin: const EdgeInsets.all(8.0),
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: CalendarDaySelector(
                      initialDate: _selectedDate,
                      onDateSelected: _onDateSelected,
                      daysWithClasses: state.daysOfWeekWithClasses,
                    ),
                  ),
                ),

                // Schedule timeline
                Expanded(
                  child: ScheduleTimeline(
                    scheduleItems: state.groupedSchedule[_selectedDayName] ?? [],
                    dayOfWeek: _selectedDayName,
                  ),
                ),
              ],
            ),
          );
        } else if (state is ScheduleError) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('Class Routine'),
              backgroundColor: Colors.white,
              foregroundColor: Colors.black,
              elevation: 0,
            ),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('Error: ${state.message}'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<ScheduleBloc>().add(LoadSchedule());
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }

        // ScheduleLoading or ScheduleInitial
        return Scaffold(
          appBar: AppBar(
            title: const Text('Class Routine'),
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            elevation: 0,
          ),
          body: const Center(
            child: CircularProgressIndicator(),
          ),
        );
      },
    );
  }
}