import 'package:flutter/material.dart';
import 'package:iubat_buddy/core/models/schedule_item.dart';

class ScheduleClassItem extends StatelessWidget {
  final ScheduleItem item;
  final Color indicatorColor;

  const ScheduleClassItem({
    super.key,
    required this.item,
    required this.indicatorColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Time column
          SizedBox(
            width: 80,
            child: Text(
              '${_formatTime(item.startTime)} - ${_formatTime(item.endTime)}',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // Timeline indicator
          Column(
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: indicatorColor,
                  shape: BoxShape.circle,
                ),
              ),
              Container(
                width: 2,
                height: 80,
                color: Colors.grey.withAlpha(76), // 0.3 * 255 = ~76
              ),
            ],
          ),

          const SizedBox(width: 12),

          // Class details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: indicatorColor.withAlpha(51), // 0.2 * 255 = ~51
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.courseName ?? item.courseCode,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getClassType(item),
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            'Teacher - ${_formatTeacherName(item.instructorName)}',
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: Colors.black54,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Consultation note if applicable
                if (_hasConsultation(item))
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.withAlpha(25), // 0.1 * 255 = ~25
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 16,
                          color: Colors.grey[700],
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Consultations on the protection of Laboratory work №1',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[800],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  String _getClassType(ScheduleItem item) {
    if (item.classType != null && item.classType!.isNotEmpty) {
      return item.classType!;
    }
    // Try to infer from room number or other properties
    if (item.roomNumber.toLowerCase().contains('lab')) {
      return 'Laboratory session';
    }
    return 'Lecture';
  }

  String _formatTeacherName(String? name) {
    if (name == null || name.isEmpty) {
      return 'N/A';
    }

    // Format as initial + last name (e.g., "J.Smith")
    final nameParts = name.split(' ');
    if (nameParts.length > 1) {
      final firstName = nameParts[0];
      final lastName = nameParts[nameParts.length - 1];
      return '${firstName[0]}.$lastName';
    }

    return name;
  }

  bool _hasConsultation(ScheduleItem item) {
    // This is a placeholder logic - in a real app, you'd have a proper field
    // For demo purposes, we'll just add a consultation to one random class
    return item.courseCode.contains('CSE') && item.section == 'A';
  }
}
