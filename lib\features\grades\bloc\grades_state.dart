part of 'grades_bloc.dart';

@immutable
abstract class GradesState extends Equatable {
  const GradesState();

  @override
  List<Object> get props => [];
}

class GradesInitial extends GradesState {}

class GradesLoading extends GradesState {}

class GradesLoaded extends GradesState {
  final List<Grade> grades;
  final double cgpa;

  const GradesLoaded(this.grades, this.cgpa);

  @override
  List<Object> get props => [grades, cgpa];
}

class GradesError extends GradesState {
  final String message;

  const GradesError(this.message);

  @override
  List<Object> get props => [message];
}