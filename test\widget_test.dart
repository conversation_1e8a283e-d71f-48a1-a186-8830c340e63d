// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:iubat_buddy/main.dart';
import 'package:iubat_buddy/core/repositories/iubat_repository.dart';
import 'package:iubat_buddy/core/services/portal_parser.dart';
import 'package:dio/dio.dart';
import 'package:cookie_jar/cookie_jar.dart';
import 'package:dio_cookie_manager/dio_cookie_manager.dart';

void main() {
  testWidgets('Counter increments smoke test', (WidgetTester tester) async {
    // Setup dependencies for MyApp
    final dio = Dio();
    // For testing, a basic CookieJar is fine, or PersistCookieJar if path_provider can be mocked/setup for tests.
    // Using a non-persistent one for simplicity in unit tests if path_provider causes issues.
    final cookieJar = CookieJar();
    dio.interceptors.add(Cookie<PERSON>anager(cookieJar));
    
    final portalParser = IUBATParser(dio: dio);
    // The test uses CookieJar, IubatRepository expects PersistCookieJar.
    // For this test, if it's just a smoke test not relying on persistence,
    // we can use a simple PersistCookieJar (it will be in-memory if no storage is given or path_provider fails in test).
    // Or, ideally, mock IubatRepository or provide a test-specific implementation.
    // For now, let's use a PersistCookieJar. It might require path_provider setup for tests.
    // If path_provider is an issue in tests, consider using Mocktail/Mockito for IubatRepository.
    final persistCookieJarForTest = PersistCookieJar(); // Will be in-memory without storage path
    final iubatRepository = IubatRepository(
      portalParser: portalParser,
      cookieJar: persistCookieJarForTest, // Pass the cookie jar
    );

    // Build our app and trigger a frame.
    await tester.pumpWidget(MyApp(iubatRepository: iubatRepository));

    // Verify that our counter starts at 0.
    expect(find.text('0'), findsOneWidget);
    expect(find.text('1'), findsNothing);

    // Tap the '+' icon and trigger a frame.
    await tester.tap(find.byIcon(Icons.add));
    await tester.pump();

    // Verify that our counter has incremented.
    expect(find.text('0'), findsNothing);
    expect(find.text('1'), findsOneWidget);
  });
}
