import 'package:flutter/material.dart'; // For TimeOfDay

class ScheduleItem {
  final String courseCode;
  final String? courseName; // Full course name, may not be available from routine page
  final String section;    // e.g., "A", "B"
  final String dayOfWeek;  // e.g., "Saturday", "Sunday"
  final TimeOfDay startTime;
  final TimeOfDay endTime;
  final String roomNumber;
  final String? instructorName; // Optional, likely not available from routine page
  final String? classType; // Optional: e.g., "Theory", "Lab" (might be inferred from room)

  ScheduleItem({
    required this.courseCode,
    this.courseName,
    required this.section,
    required this.dayOfWeek,
    required this.startTime,
    required this.endTime,
    required this.roomNumber,
    this.instructorName,
    this.classType,
  });

  // Example: factory ScheduleItem.fromMap(Map<String, dynamic> map) { ... }
  // Example: Map<String, dynamic> toMap() { ... }
}