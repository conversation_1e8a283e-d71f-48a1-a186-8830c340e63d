import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:iubat_buddy/core/models/user.dart';
import 'package:iubat_buddy/core/repositories/iubat_repository.dart';

part 'profile_event.dart';
part 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  final IubatRepository iubatRepository;

  ProfileBloc({required this.iubatRepository}) : super(ProfileInitial()) {
    on<LoadProfile>(_onLoadProfile);
    on<LoadParticularInfo>(_onLoadParticularInfo);
  }

  Future<void> _onLoadProfile(
    LoadProfile event,
    Emitter<ProfileState> emit,
  ) async {
    emit(ProfileLoading());
    try {
      final user = await iubatRepository.getUser();
      emit(ProfileLoaded(user: user));
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }

  Future<void> _onLoadParticularInfo(
    LoadParticularInfo event,
    Emitter<ProfileState> emit,
  ) async {
    emit(ProfileLoading());
    try {
      final user = await iubatRepository.getParticularInfo();
      emit(ProfileLoaded(user: user));
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }
}
