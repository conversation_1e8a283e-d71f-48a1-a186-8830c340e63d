**IUBAT Buddy Development Plan**

---

## 1. Overview and Goals

**IUBAT Buddy** is a modern mobile application providing IUBAT students with an optimized, user-centric interface for accessing the university portal (`https://iubat.online/Student`). It handles authentication, data retrieval, and parsing, then presents schedules, grades, notices, and more in a clean Flutter UI.

**Key Objectives:**

* Reverse-engineer portal endpoints (API/HTML).
* Implement secure login and session management.
* Fetch and parse academic data (courses, grades, notices, timetable).
* Build a modular parser layer to isolate scraping logic.
* Use **Flutter Bloc** for robust, testable state management.
* Craft a modern, intuitive UI/UX with a student-first design.
* Introduce local caching and offline access.
* Deploy to Android and iOS stores.

---

## 2. Technical Architecture

1. **Flutter Frontend**

   * Flutter SDK (>=3.x) with **flutter\_bloc** for state management.
   * `dio` for HTTP calls; `flutter_secure_storage` for credentials.
   * `html` package for scraping, plus JSON parsing.
2. **Bloc Structure**

   * Define feature blocs (e.g., `AuthBloc`, `CoursesBloc`, `GradesBloc`, `NoticesBloc`, `ScheduleBloc`).
   * Each bloc handles events (`LoginRequested`, `FetchCourses`, etc.) and emits states (`AuthLoading`, `CoursesLoaded`).
   * Implement a **Repository** layer (`IubatRepository`) between blocs and parser for data sources and caching logic.
3. **Parser Layer**

   * Abstract class `PortalParser` with methods:

     * `Future<void> login(...)`
     * `Future<List<Course>> getCourses()`
     * `Future<List<Grade>> getGrades()`
     * `Future<List<Notice>> getNotices()`
     * `Future<List<ScheduleItem>> getTimetable()`
   * Concrete `IUBATParser` uses DevTools-inspected endpoints or HTML scraping.
4. **Caching & Offline**

   * Use **Hive** or **sqflite** for local storage of parsed data.
   * Repository reads from cache first, then updates via parser.

---

## 3. Feature Breakdown & Milestones

| Phase                         | Duration | Deliverables |
| ----------------------------- | -------- | ------------ |
| **Phase 0: Setup & Research** | 1 week   |              |

* Scaffold project with Flutter + flutter\_bloc + dio + Hive + flutter\_secure\_storage.
* Document portal endpoints (URLs, methods, headers). |
  \| **Phase 1: Core Parsing & Repository** | 2 weeks |
* Implement `IUBATParser` methods and unit tests.
* Create `IubatRepository` integrating parser and cache. |
  \| **Phase 2: Auth & Dashboard Bloc** | 2 weeks |
* `AuthBloc` for login/logout flows.
* Dashboard screen showing key cards (Courses, Grades, Notices, Schedule). |
  \| **Phase 3: Feature Blocs & UI Screens** | 3 weeks |
* `CoursesBloc` + Courses list & detail pages.
* `GradesBloc` + Grades overview.
* `NoticesBloc` + Notices feed.
* `ScheduleBloc` + Timetable calendar view. |
  \| **Phase 4: Modern UI/UX Polish** | 2 weeks |
* Apply custom theming: IUBAT brand colors, fonts, icons.
* Implement animations (e.g., page transitions, bloc state loading).
* Responsive layout for different screen sizes.
* Accessibility checks. |
  \| **Phase 5: QA & Deployment** | 2 weeks |
* Complete end-to-end testing.
* CI/CD pipeline (GitHub Actions) for builds & tests.
* Publish to Play Store & App Store. |

---

## 4. Modern UI/UX Design Guidelines

* **Color & Typography:** Use a fresh palette based on IUBAT green & complementary neutrals. Leverage Google Fonts for readability.
* **Layout & Navigation:**

  * Bottom **NavigationBar** with icons for Dashboard, Courses, Grades, Notices, More.
  * Dashboard cards with subtle shadows, rounded corners (radius 16px), and data summaries.
* **Components & Animations:**

  * **Custom Widgets:** e.g., `ProgressCard`, `NoticeTile`, `ScheduleTimeline`.
  * **Animations:** `AnimatedSwitcher` for state changes, bloc-based loading animations, and Hero transitions between lists and details.
* **Onboarding & Feedback:**

  * First-time walkthrough highlighting features.
  * Pull-to-refresh with `RefreshIndicator`.
  * Snackbars & dialogs for error messages and confirmations.
* **Accessibility:**

  * Text scaling, contrast ratios, and semantic labels for screen readers.

---

## 5. Project Management & Collaboration

* **Version Control:** GitHub repo with feature branches.
* **Issue Tracking:** GitHub Issues and Project board.
* **CI/CD:** GitHub Actions for linting, unit tests, widget tests, and deploy artifacts.
* **Documentation:** Automated API discovery docs, bloc event/state diagrams, and style guide in README.

---

## 6. Next Steps

1. **Kickoff Workshop:** Align on Bloc architecture and design direction.
2. **Endpoint Catalog:** Finalize list of portal endpoints with sample request/response.
3. **Begin Phase 0:** Set up Flutter project and initial dependencies.

*End of Updated Plan*
