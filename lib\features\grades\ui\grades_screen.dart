import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:iubat_buddy/features/grades/bloc/grades_bloc.dart';
import 'package:iubat_buddy/features/grades/widgets/grade_item.dart';

class GradesScreen extends StatelessWidget {
  const GradesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    context.read<GradesBloc>().add(LoadGrades());

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Grades'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: BlocBuilder<GradesBloc, GradesState>(
        builder: (context, state) {
          if (state is GradesLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is GradesLoaded) {
            if (state.grades.isEmpty) {
              return const Center(child: Text('No grades found.'));
            }
            return Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Card(
                    elevation: 4,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('CGPA:', style: Theme.of(context).textTheme.headlineSmall),
                          Text(state.cgpa.toStringAsFixed(2), style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: Theme.of(context).primaryColorDark, fontWeight: FontWeight.bold)),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                    itemCount: state.grades.length,
                    itemBuilder: (context, index) {
                      final grade = state.grades[index];
                      return GradeItem(grade: grade);
                    },
                  ),
                ),
              ],
            );
          } else if (state is GradesError) {
            return Center(child: Text('Error: ${state.message}'));
          } else {
            return const Center(child: Text('Something went wrong.'));
          }
        },
      ),
    );
  }
}