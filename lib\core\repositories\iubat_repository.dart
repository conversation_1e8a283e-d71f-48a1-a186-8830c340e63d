import 'package:cookie_jar/cookie_jar.dart'; // Import for PersistCookieJar
import 'package:iubat_buddy/core/models/user.dart';
import 'package:iubat_buddy/core/services/portal_parser.dart';
import 'package:iubat_buddy/core/models/course.dart';
import 'package:iubat_buddy/core/models/grade.dart';
import 'package:iubat_buddy/core/models/notice.dart';
import 'package:iubat_buddy/core/models/schedule_item.dart';
import 'package:iubat_buddy/core/models/exam_routine.dart';
// import 'package:iubat_buddy/core/services/hive_service.dart'; // For caching, to be created

class IubatRepository {
  final PortalParser portalParser;
  final PersistCookieJar cookieJar; // Added PersistCookieJar
  // final HiveService hiveService; // Assuming a HiveService for caching

  IubatRepository({
    required this.portalParser,
    required this.cookieJar, // Added to constructor
    /*, required this.hiveService*/
  });

  Future<User> login({required String username, required String password}) async {
    final user = await portalParser.login(username: username, password: password);
    // Potentially save user details or session/token using flutter_secure_storage
    print('IubatRepository: Login successful for ${user.name} (ID: ${user.id})');
    return user;
  }

  Future<void> logout() async {
    print('IubatRepository: Initiating logout process.');
    try {
      // 1. Call portal's logout endpoint
      await portalParser.logout();
      print('IubatRepository: Portal logout successful.');

      // 2. Clear cookies from the local PersistCookieJar
      // The portal's logout URL is for the domain "iubat.online"
      final portalUri = Uri.parse('https://iubat.online');
      await cookieJar.delete(portalUri); // Deletes cookies for the specific domain
      // Or, to be more aggressive, clear all cookies if that's desired:
      // await cookieJar.deleteAll();
      print('IubatRepository: Local cookies cleared for ${portalUri.host}.');

    } catch (e) {
      print('IubatRepository: Error during logout process: $e. Attempting local cookie clear anyway.');
      // Still try to clear local cookies even if portal logout failed
      try {
        final portalUri = Uri.parse('https://iubat.online');
        await cookieJar.delete(portalUri);
        // await cookieJar.deleteAll();
        print('IubatRepository: Local cookies cleared after error.');
      } catch (clearError) {
        print('IubatRepository: Failed to clear local cookies after initial logout error: $clearError');
      }
      // Optionally rethrow the original error or a new one
      // throw Exception('Logout failed: $e');
    }
    print('IubatRepository: Logout process completed.');
  }

  Future<User> getUser() async {
    // This method assumes that if cookies are valid, fetching the dashboard will succeed
    // and the parser can extract user info.
    print('IubatRepository: Attempting to get user profile (checks auth status)');
    try {
      // The IUBATParser.getUserProfile needs the dashboard URL.
      const String dashboardUrl = 'https://iubat.online/Student/';
      // portalParser is an instance of PortalParser, which should define getUserProfile
      final user = await portalParser.getUserProfile(dashboardUrl);
      print('IubatRepository: User profile fetched successfully for ${user.name}');
      return user;
    } catch (e) {
      print('IubatRepository: Failed to get user profile (likely not logged in or session expired): $e');
      throw Exception('Not authenticated or session expired.');
    }
  }

  Future<User> getParticularInfo() async {
    print('IubatRepository: Fetching particular information');
    try {
      final user = await portalParser.getParticularInfo();
      print('IubatRepository: Particular information fetched successfully for ${user.name}');
      return user;
    } catch (e) {
      print('IubatRepository: Failed to get particular information: $e');
      throw Exception('Failed to get particular information: $e');
    }
  }

  Future<List<Course>> getCourses() async {
    // TODO: Implement caching with HiveService
    // List<Course> courses = await hiveService.getCourses();
    // if (courses.isEmpty) {
    //   courses = await portalParser.getCourses();
    //   await hiveService.saveCourses(courses);
    // }
    // return courses;
    print('IubatRepository: Fetching courses'); // Placeholder
    // final courses = await portalParser.getCourses();
    await Future.delayed(const Duration(seconds: 1)); // Simulate
    return []; // Placeholder
  }

  Future<List<Grade>> getGrades() async {
    print('IubatRepository: Fetching grades from parser');
    try {
      final grades = await portalParser.getGrades();
      print('IubatRepository: Received ${grades.length} grades from parser.');
      // TODO: Implement caching with HiveService if needed
      // await hiveService.saveGrades(grades);
      return grades;
    } catch (e) {
      print('IubatRepository: Error fetching grades: $e');
      // Rethrow or handle as appropriate for your app's error strategy
      throw Exception('Failed to get grades from repository: $e');
    }
  }

  Future<List<Notice>> getNotices() async {
    print('IubatRepository: Fetching notices'); // Placeholder
    // final notices = await portalParser.getNotices();
    await Future.delayed(const Duration(seconds: 1)); // Simulate
    return []; // Placeholder
  }

  Future<List<ScheduleItem>> getTimetable() async {
    print('IubatRepository: Fetching timetable from parser');
    try {
      final timetable = await portalParser.getTimetable();
      print('IubatRepository: Received ${timetable.length} timetable items from parser.');
      // TODO: Implement caching with HiveService if needed
      // await hiveService.saveTimetable(timetable);
      return timetable;
    } catch (e) {
      print('IubatRepository: Error fetching timetable: $e');
      throw Exception('Failed to get timetable from repository: $e');
    }
  }

  Future<ExamRoutine> getExamRoutine() async {
    print('IubatRepository: Fetching exam routine from parser');
    try {
      final examRoutine = await portalParser.getExamRoutine();
      print('IubatRepository: Received exam routine for ${examRoutine.studentName} - ${examRoutine.regularExams.length} regular exams, ${examRoutine.elctExams.length} ELCT exams');
      // TODO: Implement caching with HiveService if needed
      // await hiveService.saveExamRoutine(examRoutine);
      return examRoutine;
    } catch (e) {
      print('IubatRepository: Error fetching exam routine: $e');
      throw Exception('Failed to get exam routine from repository: $e');
    }
  }
}