import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart'; // Required for TimeOfDay
import 'package:iubat_buddy/core/models/schedule_item.dart';
import 'package:iubat_buddy/core/repositories/iubat_repository.dart';
import 'package:meta/meta.dart';

part 'schedule_event.dart';
part 'schedule_state.dart';

class ScheduleBloc extends Bloc<ScheduleEvent, ScheduleState> {
  final IubatRepository iubatRepository;

  ScheduleBloc({required this.iubatRepository}) : super(ScheduleInitial()) {
    on<LoadSchedule>(_onLoadSchedule);
  }

  Future<void> _onLoadSchedule(LoadSchedule event, Emitter<ScheduleState> emit) async {
    emit(ScheduleLoading());
    try {
      final List<ScheduleItem> scheduleItems = await iubatRepository.getTimetable();

      if (scheduleItems.isEmpty) {
        emit(const ScheduleLoaded({}, [])); // Emit empty state if no items
        return;
      }

      // Group schedule items by day
      final Map<String, List<ScheduleItem>> groupedSchedule = {};
      for (var item in scheduleItems) {
        (groupedSchedule[item.dayOfWeek] ??= []).add(item);
      }

      // Sort items within each day by start time
      groupedSchedule.forEach((day, items) {
        items.sort((a, b) {
          final aTime = a.startTime.hour * 60 + a.startTime.minute;
          final bTime = b.startTime.hour * 60 + b.startTime.minute;
          return aTime.compareTo(bTime);
        });
      });
      
      // Create an ordered list of days that have classes
      // This specific order might be desired: Sat, Sun, Mon, Tue, Wed, Thu, Fri
      const dayOrder = ["Saturday", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday"];
      List<String> daysOfWeekWithClasses = [];
      
      // Add days from dayOrder that are present in groupedSchedule
      for (String day in dayOrder) {
          if (groupedSchedule.containsKey(day)) {
              daysOfWeekWithClasses.add(day);
          }
      }
      
      // Add any other days from groupedSchedule that weren't in dayOrder (e.g., if portal uses different names)
      // This ensures all days from the fetched data are included, even if not in our predefined order.
      for (String day in groupedSchedule.keys) {
          if (!daysOfWeekWithClasses.contains(day)) {
              daysOfWeekWithClasses.add(day);
          }
      }


      emit(ScheduleLoaded(groupedSchedule, daysOfWeekWithClasses));

    } catch (e) {
      emit(ScheduleError(e.toString()));
    }
  }
}