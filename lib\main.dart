import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:iubat_buddy/core/repositories/iubat_repository.dart';
import 'package:iubat_buddy/core/services/portal_parser.dart';
import 'package:iubat_buddy/core/theme/app_theme.dart';
import 'package:iubat_buddy/features/auth/bloc/auth_bloc.dart';
import 'package:iubat_buddy/features/courses/bloc/courses_bloc.dart';
import 'package:iubat_buddy/features/grades/bloc/grades_bloc.dart';
import 'package:iubat_buddy/features/notices/bloc/notices_bloc.dart';
import 'package:iubat_buddy/features/profile/bloc/profile_bloc.dart';
import 'package:iubat_buddy/features/exam_routine/bloc/exam_routine_bloc.dart';
import 'package:iubat_buddy/features/schedule/bloc/schedule_bloc.dart';
import 'package:dio/dio.dart';
import 'package:cookie_jar/cookie_jar.dart';
import 'package:dio_cookie_manager/dio_cookie_manager.dart';
import 'package:path_provider/path_provider.dart'; // Required for PersistCookieJar
import 'dart:io'; // Required for Directory
import 'package:iubat_buddy/features/auth/ui/login_screen.dart';
import 'package:iubat_buddy/features/main/ui/main_screen.dart';

// Global variable for CookieJar to persist cookies across app sessions
PersistCookieJar? _persistCookieJar;

Future<void> main() async {
  // Ensure Flutter bindings are initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize CookieJar
  final Directory appDocDir = await getApplicationDocumentsDirectory();
  final String appDocPath = appDocDir.path;
  _persistCookieJar = PersistCookieJar(
    ignoreExpires: true, // Keep cookies even if they expire
    storage: FileStorage("$appDocPath/.cookies/"), // Storage path for cookies
  );

  // Initialize Hive for caching here if needed (after creating HiveService)
  // await Hive.initFlutter();
  // Hive.registerAdapter(UserAdapter()); // Example adapter

  final dio = Dio();
  if (_persistCookieJar != null) {
    dio.interceptors.add(CookieManager(_persistCookieJar!));
  }
  // Optionally, add other interceptors like a logger for debugging
  // dio.interceptors.add(LogInterceptor(responseBody: true, requestBody: true));

  final portalParser = IUBATParser(dio: dio);
  final iubatRepository = IubatRepository(
    portalParser: portalParser,
    cookieJar: _persistCookieJar!, // Pass the cookie jar
  );

  runApp(MyApp(iubatRepository: iubatRepository));
}

class MyApp extends StatelessWidget {
  final IubatRepository iubatRepository;
  const MyApp({super.key, required this.iubatRepository});

  @override
  Widget build(BuildContext context) {
    return MultiRepositoryProvider(
      providers: [
        RepositoryProvider.value(value: iubatRepository),
        // Provide other repositories here if any
      ],
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => AuthBloc(iubatRepository: RepositoryProvider.of<IubatRepository>(context))
              ..add(const AuthCheckStatusRequested()), // Dispatch event on creation
          ),
          BlocProvider(
            create: (context) => CoursesBloc(iubatRepository: RepositoryProvider.of<IubatRepository>(context)),
          ),
          BlocProvider(
            create: (context) => GradesBloc(iubatRepository: RepositoryProvider.of<IubatRepository>(context)),
          ),
          BlocProvider(
            create: (context) => NoticesBloc(iubatRepository: RepositoryProvider.of<IubatRepository>(context)),
          ),
          BlocProvider(
            create: (context) => ScheduleBloc(iubatRepository: RepositoryProvider.of<IubatRepository>(context)),
          ),
          BlocProvider(
            create: (context) => ProfileBloc(iubatRepository: RepositoryProvider.of<IubatRepository>(context)),
          ),
          BlocProvider(
            create: (context) => ExamRoutineBloc(iubatRepository: RepositoryProvider.of<IubatRepository>(context)),
          ),
          // Add other Blocs here as they are created
        ],
        child: MaterialApp(
          title: 'IUBAT Buddy',
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.light, // Default to light theme
          home: BlocBuilder<AuthBloc, AuthState>(
            builder: (context, state) {
              if (state is AuthSuccess) {
                return const MainScreen(); // Navigate to Main Screen with bottom navigation
              } else if (state is AuthFailure) {
                // Optionally pass the error to LoginScreen if it needs to display it initially
                return const LoginScreen(); // Show login
              } else if (state is AuthLoading) {
                return const Scaffold(body: Center(child: CircularProgressIndicator()));
              }
              return const LoginScreen(); // Default to LoginScreen
            },
          ),
        ),
      ),
    );
  }
}

// Remove or repurpose MyHomePage and _MyHomePageState as they are placeholders
class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});
  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _counter = 0;

  void _incrementCounter() {
    // Example: Trigger AuthBloc event
    // context.read<AuthBloc>().add(AuthLoginRequested(username: 'test', password: 'password'));
    setState(() {
      _counter++;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: Text(widget.title),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Text('You have pushed the button this many times:'),
            Text(
              '$_counter',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            ElevatedButton(
              onPressed: () {
                // Example: Trigger login
                context.read<AuthBloc>().add(AuthLoginRequested(username: 'testuser', password: 'password'));
              },
              child: const Text('Test Login'),
            ),
            ElevatedButton(
              onPressed: () {
                // Example: Trigger logout
                context.read<AuthBloc>().add(AuthLogoutRequested());
              },
              child: const Text('Test Logout'),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _incrementCounter,
        tooltip: 'Increment',
        child: const Icon(Icons.add),
      ),
    );
  }
}
