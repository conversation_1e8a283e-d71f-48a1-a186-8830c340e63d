import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:iubat_buddy/core/models/course.dart';
import 'package:iubat_buddy/core/repositories/iubat_repository.dart';
import 'package:meta/meta.dart';

part 'courses_event.dart';
part 'courses_state.dart';

class CoursesBloc extends Bloc<CoursesEvent, CoursesState> {
  final IubatRepository iubatRepository;

  CoursesBloc({required this.iubatRepository}) : super(CoursesInitial()) {
    on<LoadCourses>(_onLoadCourses);
  }

  Future<void> _onLoadCourses(LoadCourses event, Emitter<CoursesState> emit) async {
    emit(CoursesLoading());
    try {
      // TODO: Remove placeholder data and use repository
      // final courses = await iubatRepository.getCourses();
      // emit(CoursesLoaded(courses));

      // Placeholder data for now
      await Future.delayed(const Duration(seconds: 1)); // Simulate network delay
      final List<Course> placeholderCourses = [
        const Course(id: 'CSE101', title: 'Introduction to Computer Science', code: 'CSE101'),
        const Course(id: 'MAT102', title: 'Calculus I', code: 'MAT102'),
        const Course(id: 'PHY103', title: 'Physics for Engineers', code: 'PHY103'),
        const Course(id: 'ENG101', title: 'Freshman English', code: 'ENG101'),
        const Course(id: 'CSE202', title: 'Data Structures & Algorithms', code: 'CSE202'),
      ];
      emit(CoursesLoaded(placeholderCourses));

    } catch (e) {
      emit(CoursesError(e.toString()));
    }
  }
}