import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:meta/meta.dart';
import 'package:equatable/equatable.dart'; // Import Equatable
import 'package:iubat_buddy/core/repositories/iubat_repository.dart';
import 'package:iubat_buddy/core/models/user.dart';

part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final IubatRepository iubatRepository;

  AuthBloc({required this.iubatRepository}) : super(AuthInitial()) {
    on<AuthCheckStatusRequested>(_onAuthCheckStatusRequested);
    on<AuthLoginRequested>(_onAuthLoginRequested);
    on<AuthLogoutRequested>(_onAuthLogoutRequested);
  }

  Future<void> _onAuthCheckStatusRequested(
    AuthCheckStatusRequested event,
    Emitter<AuthState> emit,
  ) async {
    // No need to emit AuthLoading here, as this is a silent check on startup.
    // If you want a loading indicator for this, you can emit AuthLoading.
    try {
      print("AuthBloc: Checking authentication status...");
      final User user = await iubatRepository.getUser(); // This now calls the parser
      emit(AuthSuccess(user: user));
      print("AuthBloc: User already authenticated: ${user.name}");
    } catch (e) {
      // If getUser fails (e.g., session expired, no cookies), it's not an "error" for this event,
      // it just means the user is not logged in. So, we emit AuthInitial.
      print("AuthBloc: No active session found or error checking status: $e");
      emit(AuthInitial());
    }
  }

  Future<void> _onAuthLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
      try {
        final User user = await iubatRepository.login(username: event.username, password: event.password);
        emit(AuthSuccess(user: user));
      } catch (e) {
        emit(AuthFailure(error: e.toString()));
      }
    // Removed the extra }); here
  }

  Future<void> _onAuthLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading()); // Optional: show loading during logout
    try {
      await iubatRepository.logout();
      // Clear any persisted cookies explicitly if needed, though PersistCookieJar should handle it.
      // For IUBAT, logout is a GET request. We might need to implement it in IUBATParser and Repository.
      // For now, just transition to AuthInitial.
      emit(AuthInitial());
    } catch (e) {
      // If logout fails, what state should it be? Still, probably AuthInitial or a specific LogoutFailed state.
      emit(AuthFailure(error: "Logout failed: ${e.toString()}")); // Or emit(AuthInitial());
    }
  }
}