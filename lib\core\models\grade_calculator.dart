class GradeCalculator {
  final double finalExamMarks;
  final double midtermMarks;
  final double assignmentMarks;
  final double quizMarks;
  final double otherMarks;

  const GradeCalculator({
    required this.finalExamMarks,
    required this.midtermMarks,
    required this.assignmentMarks,
    required this.quizMarks,
    this.otherMarks = 0.0,
  });

  /// Calculate the total percentage based on IUBAT grading system
  /// Final: 50%, Midterm: 25%, Assignment: 10%, Quiz: 10%, Other: 5%
  double calculateTotalPercentage() {
    final finalPercentage = (finalExamMarks / 100) * 50;
    final midtermPercentage = (midtermMarks / 100) * 25;
    final assignmentPercentage = (assignmentMarks / 100) * 10;
    final quizPercentage = (quizMarks / 100) * 10;
    final otherPercentage = (otherMarks / 100) * 5;

    return finalPercentage + midtermPercentage + assignmentPercentage + quizPercentage + otherPercentage;
  }

  /// Get letter grade based on percentage
  String getLetterGrade() {
    final percentage = calculateTotalPercentage();
    
    if (percentage >= 80) return 'A+';
    if (percentage >= 75) return 'A';
    if (percentage >= 70) return 'A-';
    if (percentage >= 65) return 'B+';
    if (percentage >= 60) return 'B';
    if (percentage >= 55) return 'B-';
    if (percentage >= 50) return 'C+';
    if (percentage >= 45) return 'C';
    if (percentage >= 40) return 'D';
    return 'F';
  }

  /// Get GPA points based on percentage
  double getGpaPoints() {
    final percentage = calculateTotalPercentage();
    
    if (percentage >= 80) return 4.0;
    if (percentage >= 75) return 3.75;
    if (percentage >= 70) return 3.5;
    if (percentage >= 65) return 3.25;
    if (percentage >= 60) return 3.0;
    if (percentage >= 55) return 2.75;
    if (percentage >= 50) return 2.5;
    if (percentage >= 45) return 2.25;
    if (percentage >= 40) return 2.0;
    return 0.0;
  }

  /// Get grade color based on performance
  static int getGradeColor(String letterGrade) {
    switch (letterGrade) {
      case 'A+':
        return 0xFF2E7D32; // Dark Green
      case 'A':
        return 0xFF388E3C; // Green
      case 'A-':
        return 0xFF43A047; // Light Green
      case 'B+':
        return 0xFF689F38; // Light Green
      case 'B':
        return 0xFF827717; // Lime
      case 'B-':
        return 0xFFAFB42B; // Lime
      case 'C+':
        return 0xFFFF8F00; // Orange
      case 'C':
        return 0xFFFF6F00; // Deep Orange
      case 'D':
        return 0xFFE65100; // Deep Orange
      case 'F':
        return 0xFFD32F2F; // Red
      default:
        return 0xFF757575; // Grey
    }
  }

  /// Calculate required marks for target grade
  static Map<String, double> calculateRequiredMarks({
    required double targetPercentage,
    double? midtermMarks,
    double? assignmentMarks,
    double? quizMarks,
    double? otherMarks,
  }) {
    final midterm = (midtermMarks ?? 0) / 100 * 25;
    final assignment = (assignmentMarks ?? 0) / 100 * 10;
    final quiz = (quizMarks ?? 0) / 100 * 10;
    final other = (otherMarks ?? 0) / 100 * 5;
    
    final currentTotal = midterm + assignment + quiz + other;
    final requiredFromFinal = targetPercentage - currentTotal;
    final requiredFinalMarks = (requiredFromFinal / 50) * 100;

    return {
      'requiredFinalMarks': requiredFinalMarks.clamp(0, 100),
      'currentTotal': currentTotal,
      'requiredFromFinal': requiredFromFinal,
    };
  }
}

/// Grade calculation result model
class GradeCalculationResult {
  final double totalPercentage;
  final String letterGrade;
  final double gpaPoints;
  final int gradeColor;
  final Map<String, double> breakdown;

  const GradeCalculationResult({
    required this.totalPercentage,
    required this.letterGrade,
    required this.gpaPoints,
    required this.gradeColor,
    required this.breakdown,
  });

  factory GradeCalculationResult.fromCalculator(GradeCalculator calculator) {
    final totalPercentage = calculator.calculateTotalPercentage();
    final letterGrade = calculator.getLetterGrade();
    
    return GradeCalculationResult(
      totalPercentage: totalPercentage,
      letterGrade: letterGrade,
      gpaPoints: calculator.getGpaPoints(),
      gradeColor: GradeCalculator.getGradeColor(letterGrade),
      breakdown: {
        'final': (calculator.finalExamMarks / 100) * 50,
        'midterm': (calculator.midtermMarks / 100) * 25,
        'assignment': (calculator.assignmentMarks / 100) * 10,
        'quiz': (calculator.quizMarks / 100) * 10,
        'other': (calculator.otherMarks / 100) * 5,
      },
    );
  }
}
