# IUBAT Buddy - Project Progress

This document outlines the features implemented in the IUBAT Buddy app, what's planned, and a brief overview of how key features work.

## Implemented Features

### 1. User Authentication
    *   **Login:** Users can log in using their IUBAT student portal credentials.
        *   **How it works:** The app sends a POST request with username and password to `https://iubat.online/Student/Login/LoginUser`. Successful login establishes a session via cookies.
    *   **Session Persistence (Login Retention):** Users remain logged in across app restarts.
        *   **How it works:** Cookies received from the portal are stored securely on the device using `PersistCookieJar`. On app startup, an attempt is made to fetch user profile data; if successful (due to valid cookies), the user is considered logged in.
    *   **Logout:** Users can log out, which clears their session.
        *   **How it works:** The app makes a GET request to `https://iubat.online/Student/logout`. It then clears the locally stored cookies for the `iubat.online` domain.
    *   **User Profile Display (Initial):** Basic user information (Name, ID, Program, Profile Picture) is fetched and was displayed on the dashboard. (Note: Profile picture and detailed user info display on dashboard was recently removed as per new UI direction).
        *   **How it works:** After login or on app start (if session persists), user data is parsed from the main dashboard page (`https://iubat.online/Student/`).

### 2. Semester Grades & CGPA
    *   **Display:** Users can view their semester-wise grades and calculated CGPA.
    *   **How it works:**
        *   The app navigates to `https://iubat.online/Student/Student-result`.
        *   It parses the HTML table containing course codes, names, credits, grades, grade points, and remarks for each semester.
        *   It also extracts the overall CGPA mentioned on the page.
        *   The data is structured into `Grade` and `CourseGrade` models and displayed in a list format.

### 3. Class Schedule (Routine)
    *   **Display:** Users can view their weekly class schedule.
    *   **How it works:**
        *   The app navigates to `https://iubat.online/Student/Student-routine`.
        *   It parses the HTML table(s) representing the class timetable. This includes extracting day, time slots, course code, course name (if available), section, room number, and instructor name.
        *   The schedule is displayed in a tabbed view, with each tab representing a day of the week. Classes are listed chronologically for each day.
        *   `TimeOfDay` objects are used for start and end times.

### 4. Dashboard UI (Initial Version with Grid)
    *   **Layout:** The main dashboard now uses a `GridView` to display navigation cards for different features.
    *   **Navigation Cards:**
        *   Semester Result (Grades)
        *   Class Routine (Schedule)
        *   Notices (Placeholder screen)
        *   Particular Info (Placeholder action)
        *   Exam Routine (Placeholder action)
        *   Account Info (Placeholder action)
        *   Bus Schedule (Placeholder action)
        *   CIS Survey (Placeholder action)
    *   **How it works:** The `DashboardScreen` widget uses a `CustomScrollView` with a `SliverGrid` to arrange `_buildDashboardCard` widgets. Each card has an icon, title, and an `onTap` action to navigate to the respective feature screen or show a placeholder message.

## Partially Implemented / In Progress

### 1. Font Customization
    *   **"Inter" Font:** Added "Inter" font files (`.ttf`) to `assets/fonts/` and declared them in `pubspec.yaml`.
    *   **Next Step:** Set "Inter" as the default `fontFamily` in the app's `ThemeData`.

### 2. Bottom Navigation Bar
    *   **Concept:** A bottom navigation bar with "Home", "Routines", "Buses", "Profile" sections is planned.
    *   **Next Step:** Implement the main app structure (e.g., a `MainScreen` widget) to host the `BottomNavigationBar` and manage the display of different page views.

## Planned Features (Not Yet Started or Placeholder)

### 1. "Home" Screen (Smart Dashboard)
    *   **Concept:** A dynamic home screen that shows today's and tomorrow's class routine. If an exam is near, it will prioritize showing the exam routine. Aims for a "beautiful and sleek" UI/UX.
    *   **Depends on:** Class Routine, Exam Routine features being fully implemented.

### 2. "Routines" Section (Bottom Navigation)
    *   **Concept:** Likely a dedicated section to access Class Routine and Final Exam Routine.
    *   **Depends on:** Class Routine (implemented), Final Exam Routine (to be implemented).

### 3. "Buses" Section (Bottom Navigation)
    *   **Concept:** Display bus schedules.
    *   **How it will work (planned):** Fetch and parse data from `https://iubat.online/Student/BusSchedule`.

### 4. "Profile" Section (Bottom Navigation)
    *   **Concept:** Display "Particular Information" for the student.
    *   **How it will work (planned):** Fetch and parse data from `https://iubat.online/Student/Student-particular`. This will likely update/augment the `User` model.

### 5. Notices
    *   **Concept:** Fetch and display notices from the portal.
    *   **How it will work (planned):** Parse the marquee/notice board on the main dashboard (`https://iubat.online/Student/`) and potentially fetch details for each notice by navigating to their individual links (e.g., `https://iubat.online/Student/StudentNotice/NoticeDetails/100`).

### 6. Final Exam Routine
    *   **Concept:** Display the final exam schedule.
    *   **How it will work (planned):** Fetch and parse data from `https://iubat.online/Student/Student-final-routine`.

### 7. Account Information (External Link)
    *   **Concept:** Provide a quick link to the external payment portal.
    *   **How it will work (planned):** Use the `url_launcher` package to open `https://iubatpayment.net/` in a browser.

### 8. CIS - Course Instruction Survey
    *   **Concept:** Allow users to view or interact with CIS.
    *   **How it will work (planned):** Fetch and parse data from `https://iubat.online/Student/Student-CIS`. The exact functionality (view only, or attempt to fill if possible via web view/parsing) needs to be determined.

### 9. My Courses (Dedicated Screen)
    *   **Concept:** A screen listing all registered courses for the current semester with details.
    *   **Status:** Currently a placeholder screen exists. The data source and exact content need to be defined.

### 10. UI/UX Enhancements
    *   **Overall Theme:** Implement a consistent and visually appealing theme (colors, typography with "Inter", component styles).
    *   **Sleek Design:** Focus on modern, clean, and intuitive UI/UX across all screens as requested.
    *   **Loading/Error States:** Improve visual feedback for loading data and handling errors gracefully.

## How Key Systems Work

### Portal Parsing (`IUBATParser`)
*   Uses the `dio` package for HTTP requests to the IUBAT portal.
*   Manages cookies using `dio_cookie_manager` and `PersistCookieJar` for session handling.
*   Uses the `html` package to parse HTML responses from the portal.
*   Specific parsing logic is implemented for each feature (login, user profile, grades, class routine) to extract relevant data from HTML elements (tables, divs, spans, etc.) using CSS selectors.
*   Error handling is included to manage network issues or unexpected HTML structures.

### State Management (`flutter_bloc`)
*   Each major feature (Auth, Grades, Schedule, etc.) has its own BLoC (Business Logic Component).
*   **Events:** Represent user actions or lifecycle events (e.g., `LoadGrades`, `LoginRequested`).
*   **States:** Represent the UI state (e.g., `GradesLoading`, `GradesLoaded`, `GradesError`).
*   **Blocs:** Receive events, interact with the `IubatRepository` to fetch/process data, and emit new states to update the UI.
*   UI widgets listen to BLoC states using `BlocBuilder` or `BlocListener` and rebuild accordingly.

### Repository Pattern (`IubatRepository`)
*   Acts as a single source of truth for data.
*   Mediates between BLoCs and the `PortalParser` (data source).
*   Handles logic for fetching data from the parser and could potentially integrate caching mechanisms in the future.
*   Provides a clean API for BLoCs to request data (e.g., `getGrades()`, `getTimetable()`).

---
