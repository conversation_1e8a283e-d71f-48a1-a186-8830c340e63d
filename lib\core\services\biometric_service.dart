import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth/error_codes.dart' as auth_error;

class BiometricService {
  static final LocalAuthentication _localAuth = LocalAuthentication();

  /// Check if biometric authentication is available on the device
  static Future<bool> isBiometricAvailable() async {
    try {
      final bool isAvailable = await _localAuth.canCheckBiometrics;
      final bool isDeviceSupported = await _localAuth.isDeviceSupported();
      return isAvailable && isDeviceSupported;
    } catch (e) {
      return false;
    }
  }

  /// Get available biometric types
  static Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      return [];
    }
  }

  /// Authenticate using biometrics
  static Future<BiometricAuthResult> authenticate({
    String reason = 'Please authenticate to access CGPA',
    bool biometricOnly = false,
  }) async {
    try {
      final bool isAvailable = await isBiometricAvailable();
      if (!isAvailable) {
        return BiometricAuthResult(
          success: false,
          errorMessage: 'Biometric authentication is not available on this device',
          errorType: BiometricErrorType.notAvailable,
        );
      }

      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: reason,
        options: AuthenticationOptions(
          biometricOnly: biometricOnly,
          stickyAuth: true,
          sensitiveTransaction: true,
        ),
      );

      if (didAuthenticate) {
        return BiometricAuthResult(success: true);
      } else {
        return BiometricAuthResult(
          success: false,
          errorMessage: 'Authentication was cancelled or failed',
          errorType: BiometricErrorType.userCancel,
        );
      }
    } on PlatformException catch (e) {
      String errorMessage;
      BiometricErrorType errorType;

      switch (e.code) {
        case auth_error.notAvailable:
          errorMessage = 'Biometric authentication is not available';
          errorType = BiometricErrorType.notAvailable;
          break;
        case auth_error.notEnrolled:
          errorMessage = 'No biometrics enrolled. Please set up fingerprint or face recognition in device settings';
          errorType = BiometricErrorType.notEnrolled;
          break;
        case auth_error.lockedOut:
          errorMessage = 'Biometric authentication is temporarily locked. Please try again later';
          errorType = BiometricErrorType.lockedOut;
          break;
        case auth_error.permanentlyLockedOut:
          errorMessage = 'Biometric authentication is permanently locked. Please use device passcode';
          errorType = BiometricErrorType.permanentlyLockedOut;
          break;
        case auth_error.biometricOnlyNotSupported:
          errorMessage = 'Biometric-only authentication is not supported';
          errorType = BiometricErrorType.notSupported;
          break;
        default:
          errorMessage = 'Authentication error: ${e.message ?? 'Unknown error'}';
          errorType = BiometricErrorType.unknown;
      }

      return BiometricAuthResult(
        success: false,
        errorMessage: errorMessage,
        errorType: errorType,
      );
    } catch (e) {
      return BiometricAuthResult(
        success: false,
        errorMessage: 'An unexpected error occurred: $e',
        errorType: BiometricErrorType.unknown,
      );
    }
  }

  /// Get biometric type display name
  static String getBiometricTypeName(BiometricType type) {
    switch (type) {
      case BiometricType.face:
        return 'Face Recognition';
      case BiometricType.fingerprint:
        return 'Fingerprint';
      case BiometricType.iris:
        return 'Iris Recognition';
      case BiometricType.strong:
        return 'Strong Biometric';
      case BiometricType.weak:
        return 'Weak Biometric';
      default:
        return 'Biometric';
    }
  }

  /// Get primary biometric type available
  static Future<String> getPrimaryBiometricType() async {
    final biometrics = await getAvailableBiometrics();
    if (biometrics.isEmpty) return 'Biometric';
    
    // Prioritize face recognition, then fingerprint
    if (biometrics.contains(BiometricType.face)) {
      return 'Face Recognition';
    } else if (biometrics.contains(BiometricType.fingerprint)) {
      return 'Fingerprint';
    } else {
      return getBiometricTypeName(biometrics.first);
    }
  }
}

/// Result of biometric authentication
class BiometricAuthResult {
  final bool success;
  final String? errorMessage;
  final BiometricErrorType? errorType;

  const BiometricAuthResult({
    required this.success,
    this.errorMessage,
    this.errorType,
  });
}

/// Types of biometric authentication errors
enum BiometricErrorType {
  notAvailable,
  notEnrolled,
  notSupported,
  lockedOut,
  permanentlyLockedOut,
  userCancel,
  unknown,
}
