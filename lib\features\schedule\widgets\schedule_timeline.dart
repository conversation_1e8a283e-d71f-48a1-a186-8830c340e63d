import 'package:flutter/material.dart';
import 'package:iubat_buddy/core/models/schedule_item.dart';
import 'package:iubat_buddy/features/schedule/widgets/schedule_class_item.dart';

class ScheduleTimeline extends StatelessWidget {
  final List<ScheduleItem> scheduleItems;
  final String dayOfWeek;

  const ScheduleTimeline({
    super.key,
    required this.scheduleItems,
    required this.dayOfWeek,
  });

  @override
  Widget build(BuildContext context) {
    if (scheduleItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.event_busy,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No classes scheduled for $dayOfWeek',
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Select another day to view classes',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: scheduleItems.length,
      itemBuilder: (context, index) {
        final item = scheduleItems[index];
        // Assign different colors to different courses
        final color = _getColorForCourse(item.courseCode);
        return ScheduleClassItem(
          item: item,
          indicatorColor: color,
        );
      },
    );
  }

  Color _getColorForCourse(String courseCode) {
    // Generate a consistent color based on the course code
    final colors = [
      const Color(0xFFE57373), // Red
      const Color(0xFF64B5F6), // Blue
      const Color(0xFF81C784), // Green
      const Color(0xFFFFB74D), // Orange
      const Color(0xFFBA68C8), // Purple
      const Color(0xFF4DB6AC), // Teal
      const Color(0xFFFFD54F), // Amber
    ];

    // Use a simple hash function to get a consistent index
    int hash = 0;
    for (int i = 0; i < courseCode.length; i++) {
      hash = (hash + courseCode.codeUnitAt(i)) % colors.length;
    }

    return colors[hash];
  }
}