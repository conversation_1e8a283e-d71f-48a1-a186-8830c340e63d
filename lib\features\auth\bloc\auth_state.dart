part of 'auth_bloc.dart';

// Note: `User` model will be imported in `auth_bloc.dart`
// Note: `@immutable` is typically imported from `package:meta/meta.dart` in the main library file.

@immutable
abstract class AuthState {}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class AuthSuccess extends AuthState {
  final User user;
  AuthSuccess({required this.user});

  // If using Equatable:
  // @override
  // List<Object> get props => [user];
}

class AuthFailure extends AuthState {
  final String error;

  AuthFailure({required this.error});
}