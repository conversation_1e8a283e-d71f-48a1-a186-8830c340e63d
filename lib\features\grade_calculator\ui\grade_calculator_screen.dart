import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:iubat_buddy/core/models/grade_calculator.dart';

class GradeCalculatorScreen extends StatefulWidget {
  const GradeCalculatorScreen({super.key});

  @override
  State<GradeCalculatorScreen> createState() => _GradeCalculatorScreenState();
}

class _GradeCalculatorScreenState extends State<GradeCalculatorScreen> {
  final _formKey = GlobalKey<FormState>();
  final _finalController = TextEditingController();
  final _midtermController = TextEditingController();
  final _assignmentController = TextEditingController();
  final _quizController = TextEditingController();
  final _otherController = TextEditingController();

  GradeCalculationResult? _result;
  String _selectedTargetGrade = 'A+';

  final List<String> _targetGrades = ['A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'D'];
  final Map<String, double> _gradePercentages = {
    'A+': 80, 'A': 75, 'A-': 70, 'B+': 65, 'B': 60,
    'B-': 55, 'C+': 50, 'C': 45, 'D': 40,
  };

  @override
  void dispose() {
    _finalController.dispose();
    _midtermController.dispose();
    _assignmentController.dispose();
    _quizController.dispose();
    _otherController.dispose();
    super.dispose();
  }

  void _calculateGrade() {
    if (_formKey.currentState!.validate()) {
      final calculator = GradeCalculator(
        finalExamMarks: double.tryParse(_finalController.text) ?? 0,
        midtermMarks: double.tryParse(_midtermController.text) ?? 0,
        assignmentMarks: double.tryParse(_assignmentController.text) ?? 0,
        quizMarks: double.tryParse(_quizController.text) ?? 0,
        otherMarks: double.tryParse(_otherController.text) ?? 0,
      );

      setState(() {
        _result = GradeCalculationResult.fromCalculator(calculator);
      });
    }
  }

  void _clearAll() {
    _finalController.clear();
    _midtermController.clear();
    _assignmentController.clear();
    _quizController.clear();
    _otherController.clear();
    setState(() {
      _result = null;
    });
  }

  Map<String, double> _calculateRequiredMarks() {
    final targetPercentage = _gradePercentages[_selectedTargetGrade] ?? 80;
    return GradeCalculator.calculateRequiredMarks(
      targetPercentage: targetPercentage,
      midtermMarks: double.tryParse(_midtermController.text),
      assignmentMarks: double.tryParse(_assignmentController.text),
      quizMarks: double.tryParse(_quizController.text),
      otherMarks: double.tryParse(_otherController.text),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Grade Calculator'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _clearAll,
            tooltip: 'Clear All',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Information Card
              Card(
                color: Theme.of(context).primaryColor.withAlpha(20),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'IUBAT Grading System',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text('• Final Exam: 50%'),
                      const Text('• Midterm: 25%'),
                      const Text('• Assignment: 10%'),
                      const Text('• Quiz: 10%'),
                      const Text('• Other: 5%'),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Input Fields
              _buildInputField(
                controller: _finalController,
                label: 'Final Exam Marks',
                hint: 'Enter marks out of 100',
                icon: Icons.school,
                color: Colors.red,
              ),
              
              _buildInputField(
                controller: _midtermController,
                label: 'Midterm Marks',
                hint: 'Enter marks out of 100',
                icon: Icons.quiz,
                color: Colors.orange,
              ),
              
              _buildInputField(
                controller: _assignmentController,
                label: 'Assignment Marks',
                hint: 'Enter marks out of 100',
                icon: Icons.assignment,
                color: Colors.blue,
              ),
              
              _buildInputField(
                controller: _quizController,
                label: 'Quiz Marks',
                hint: 'Enter marks out of 100',
                icon: Icons.help_outline,
                color: Colors.green,
              ),
              
              _buildInputField(
                controller: _otherController,
                label: 'Other Marks (Optional)',
                hint: 'Enter marks out of 100',
                icon: Icons.more_horiz,
                color: Colors.purple,
                required: false,
              ),

              const SizedBox(height: 24),

              // Calculate Button
              ElevatedButton.icon(
                onPressed: _calculateGrade,
                icon: const Icon(Icons.calculate),
                label: const Text('Calculate Grade'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),

              if (_result != null) ...[
                const SizedBox(height: 24),
                _buildResultCard(),
              ],

              const SizedBox(height: 24),
              _buildTargetGradeCalculator(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    required Color color,
    bool required = true,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: TextFormField(
        controller: controller,
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
        ],
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          prefixIcon: Icon(icon, color: color),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: color, width: 2),
          ),
        ),
        validator: required ? (value) {
          if (value == null || value.isEmpty) {
            return 'Please enter $label';
          }
          final marks = double.tryParse(value);
          if (marks == null || marks < 0 || marks > 100) {
            return 'Please enter valid marks (0-100)';
          }
          return null;
        } : null,
        onChanged: (_) {
          if (_result != null) {
            _calculateGrade();
          }
        },
      ),
    );
  }

  Widget _buildResultCard() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              Color(_result!.gradeColor).withAlpha(100),
              Color(_result!.gradeColor).withAlpha(50),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Your Grade',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    Text(
                      _result!.letterGrade,
                      style: Theme.of(context).textTheme.displayMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Color(_result!.gradeColor),
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Percentage',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    Text(
                      '${_result!.totalPercentage.toStringAsFixed(1)}%',
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'GPA Points',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    Text(
                      _result!.gpaPoints.toStringAsFixed(2),
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            
            // Breakdown
            Text(
              'Grade Breakdown',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            ...(_result!.breakdown.entries.map((entry) {
              final percentage = entry.value;
              final label = entry.key.toUpperCase();
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('$label:'),
                    Text('${percentage.toStringAsFixed(1)}%'),
                  ],
                ),
              );
            }).toList()),
          ],
        ),
      ),
    );
  }

  Widget _buildTargetGradeCalculator() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Target Grade Calculator',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: Text(
                    'I want to achieve:',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                DropdownButton<String>(
                  value: _selectedTargetGrade,
                  items: _targetGrades.map((grade) {
                    return DropdownMenuItem(
                      value: grade,
                      child: Text(grade),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedTargetGrade = value!;
                    });
                  },
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Builder(
              builder: (context) {
                final required = _calculateRequiredMarks();
                final requiredFinal = required['requiredFinalMarks'] ?? 0;
                final currentTotal = required['currentTotal'] ?? 0;
                
                return Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withAlpha(20),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current Total: ${currentTotal.toStringAsFixed(1)}%',
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        requiredFinal <= 100 
                          ? 'Required Final Exam: ${requiredFinal.toStringAsFixed(1)}%'
                          : 'Target grade not achievable with current marks',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: requiredFinal <= 100 ? Colors.green : Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
