# Plan: Reverse-Engineering the IUBAT Portal Login

This document outlines the steps to reverse-engineer the login mechanism of `https://iubat.online/Student`. This is part of "Phase 0: Setup & Research" for the IUBAT Buddy project.

## 1. Preparation & Tools:
    *   Use a modern web browser (e.g., Chrome, Firefox).
    *   Open the browser's Developer Tools (usually by pressing F12 or right-clicking on the page and selecting "Inspect" or "Inspect Element").

## 2. Initial Observation (Login Page Analysis):
    *   Navigate to `https://iubat.online/Student/login`.
    *   In Developer Tools, go to the "Network" tab. Enable "Preserve log" (or a similar option to keep requests across page navigations).
    *   Visually inspect the login form fields. Note their `name` attributes if visible by inspecting the HTML elements (e.g., username field, password field).
    *   Look for any hidden input fields within the `<form>` tag. These often carry important state or security tokens (e.g., `__VIEWSTATE`, `__EVENTVALIDATION`, CSRF tokens).

## 3. Simulate Login Attempt & Capture Request:
    *   Enter dummy (or test, if available and ethical) credentials into the login form.
    *   Click the login button.
    *   Observe the network requests in the "Network" tab. Identify the main request that submits the login data. This is typically a `POST` request to a specific URL.

## 4. Detailed Request Analysis:
    *   **Identify the Login Endpoint URL:** This is the "Request URL" of the submission request.
    *   **Note the Request Method:** (e.g., `POST`).
    *   **Examine Request Headers:** Pay attention to:
        *   `Content-Type`: (e.g., `application/x-www-form-urlencoded`, `application/json`)
        *   `Cookie`: Any cookies sent with the request.
        *   `Referer`: The page that initiated the request.
        *   `User-Agent`: Browser identification.
        *   Any custom headers (e.g., `X-CSRF-Token`, `X-Requested-With`).
    *   **Inspect Request Payload/Form Data:**
        *   This section shows exactly what data was sent.
        *   List all key-value pairs (e.g., `txtId`, `txtPassword`, `__EVENTTARGET`, `__EVENTARGUMENT`, `__VIEWSTATE`, `__VIEWSTATEGENERATOR`, `__EVENTVALIDATION`, `btnLogin`). Note the exact names as these will be needed for our application to mimic the request.

## 5. Response Analysis (Successful vs. Failed Login):
    *   **Successful Login:**
        *   **Status Code:** (e.g., `200 OK` if the next page loads, or `302 Found` if it redirects).
        *   **Response Headers:** Look for `Set-Cookie` headers, as these establish the session. Note the names and values of these cookies.
        *   **Response Body:** How does the page change? Is there a redirect to a dashboard?
    *   **Failed Login:**
        *   **Status Code:** (e.g., `200 OK` but with an error message on the page, or a `4xx` error).
        *   **Response Body:** Identify how error messages are presented (e.g., specific text, HTML structure).

## 6. Identify Dynamic Values & Security Tokens:
    *   Determine if values like `__VIEWSTATE`, `__EVENTVALIDATION`, or any CSRF tokens are static or change with each page load.
    *   If they are dynamic, you'll need to figure out how to fetch them *before* attempting a login (usually by first making a `GET` request to the login page and parsing them from the HTML response).

## 7. Document Findings:
    *   All findings should be recorded meticulously. This includes:
        *   Login Page URL
        *   Login Submission URL (Endpoint)
        *   Request Method
        *   Required Request Headers (and their typical values)
        *   Form Data / Payload Parameters (detailing name, typical value, and if they are static or dynamic)
        *   Process for obtaining dynamic parameters (e.g., "Fetch login page HTML, parse `__VIEWSTATE` value")
        *   Success Indicators (status codes, specific cookies set, redirect URLs, key elements/text on the success page)
        *   Failure Indicators (status codes, specific error messages or HTML patterns on the login page)

## Mermaid Diagram of the Process

```mermaid
graph TD
    A[Start: Open https://iubat.online/Student/login] --> B{Open DevTools (Network Tab)};
    B --> C{Inspect Login Form HTML};
    C --> D{Note Field Names & Hidden Inputs};
    D --> E{Attempt Login};
    E --> F{Identify Login POST Request};
    F --> G{Analyze Request: URL, Method, Headers, Payload};
    G --> H{Analyze Response: Status, Headers (Set-Cookie), Body};
    H --> I{Identify Dynamic Tokens (e.g., VIEWSTATE, CSRF)};
    I --> J{Determine How to Fetch Dynamic Tokens};
    J --> K[Document All Findings in Detail];