import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:iubat_buddy/features/notices/bloc/notices_bloc.dart';
import 'package:iubat_buddy/features/notices/widgets/notice_tile.dart';

class NoticesScreen extends StatelessWidget {
  const NoticesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    context.read<NoticesBloc>().add(LoadNotices());

    return Scaffold(
      appBar: AppBar(
        title: const Text('University Notices'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: BlocBuilder<NoticesBloc, NoticesState>(
        builder: (context, state) {
          if (state is NoticesLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is NoticesLoaded) {
            if (state.notices.isEmpty) {
              return const Center(child: Text('No notices found.'));
            }
            return ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: state.notices.length,
              itemBuilder: (context, index) {
                final notice = state.notices[index];
                return NoticeTile(
                  notice: notice,
                  onTap: () {
                    // Navigate to notice details screen or open attachment
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Tapped on: ${notice.title}')),
                    );
                    if (notice.attachmentUrl != null && notice.attachmentUrl!.isNotEmpty) {
                      // In a real app, use url_launcher to open the URL
                      print('Attachment URL: ${notice.attachmentUrl}');
                    }
                  },
                );
              },
            );
          } else if (state is NoticesError) {
            return Center(child: Text('Error: ${state.message}'));
          } else {
            return const Center(child: Text('Something went wrong.'));
          }
        },
      ),
    );
  }
}