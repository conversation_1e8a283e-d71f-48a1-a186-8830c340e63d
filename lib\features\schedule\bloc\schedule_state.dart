part of 'schedule_bloc.dart';

@immutable
abstract class ScheduleState extends Equatable {
  const ScheduleState();

  @override
  List<Object> get props => [];
}

class ScheduleInitial extends ScheduleState {}

class ScheduleLoading extends ScheduleState {}

class ScheduleLoaded extends ScheduleState {
  // Store schedule items grouped by day of the week
  final Map<String, List<ScheduleItem>> groupedSchedule;
  // Optionally, keep the original flat list if needed elsewhere, or a list of unique days
  final List<String> daysOfWeekWithClasses; // To maintain order if needed

  const ScheduleLoaded(this.groupedSchedule, this.daysOfWeekWithClasses);

  @override
  List<Object> get props => [groupedSchedule, daysOfWeekWithClasses];
}

class ScheduleError extends ScheduleState {
  final String message;

  const ScheduleError(this.message);

  @override
  List<Object> get props => [message];
}