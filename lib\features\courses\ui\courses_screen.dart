import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:iubat_buddy/features/courses/bloc/courses_bloc.dart';
import 'package:iubat_buddy/features/courses/widgets/course_card.dart';

class CoursesScreen extends StatelessWidget {
  const CoursesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Dispatch LoadCourses event when the screen is built
    context.read<CoursesBloc>().add(LoadCourses());

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Courses'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: BlocBuilder<CoursesBloc, CoursesState>(
        builder: (context, state) {
          if (state is CoursesLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is CoursesLoaded) {
            if (state.courses.isEmpty) {
              return const Center(child: Text('No courses found.'));
            }
            return ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: state.courses.length,
              itemBuilder: (context, index) {
                final course = state.courses[index];
                return CourseCard(
                  course: course,
                  onTap: () {
                    // Navigate to course details screen or show more info
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Tapped on ${course.title}')),
                    );
                  },
                );
              },
            );
          } else if (state is CoursesError) {
            return Center(child: Text('Error: ${state.message}'));
          } else {
            return const Center(child: Text('Something went wrong.'));
          }
        },
      ),
    );
  }
}