import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:iubat_buddy/core/models/exam_routine.dart';
import 'package:iubat_buddy/core/repositories/iubat_repository.dart';

// Events
abstract class ExamRoutineEvent extends Equatable {
  const ExamRoutineEvent();

  @override
  List<Object> get props => [];
}

class ExamRoutineLoadRequested extends ExamRoutineEvent {}

class ExamRoutineRefreshRequested extends ExamRoutineEvent {}

// States
abstract class ExamRoutineState extends Equatable {
  const ExamRoutineState();

  @override
  List<Object> get props => [];
}

class ExamRoutineInitial extends ExamRoutineState {}

class ExamRoutineLoading extends ExamRoutineState {}

class ExamRoutineLoaded extends ExamRoutineState {
  final ExamRoutine examRoutine;

  const ExamRoutineLoaded({required this.examRoutine});

  @override
  List<Object> get props => [examRoutine];
}

class ExamRoutineError extends ExamRoutineState {
  final String message;

  const ExamRoutineError({required this.message});

  @override
  List<Object> get props => [message];
}

// BLoC
class ExamRoutineBloc extends Bloc<ExamRoutineEvent, ExamRoutineState> {
  final IubatRepository iubatRepository;

  ExamRoutineBloc({required this.iubatRepository}) : super(ExamRoutineInitial()) {
    on<ExamRoutineLoadRequested>(_onExamRoutineLoadRequested);
    on<ExamRoutineRefreshRequested>(_onExamRoutineRefreshRequested);
  }

  Future<void> _onExamRoutineLoadRequested(
    ExamRoutineLoadRequested event,
    Emitter<ExamRoutineState> emit,
  ) async {
    emit(ExamRoutineLoading());
    try {
      final examRoutine = await iubatRepository.getExamRoutine();
      emit(ExamRoutineLoaded(examRoutine: examRoutine));
    } catch (e) {
      emit(ExamRoutineError(message: e.toString()));
    }
  }

  Future<void> _onExamRoutineRefreshRequested(
    ExamRoutineRefreshRequested event,
    Emitter<ExamRoutineState> emit,
  ) async {
    // Don't show loading state for refresh, just update data
    try {
      final examRoutine = await iubatRepository.getExamRoutine();
      emit(ExamRoutineLoaded(examRoutine: examRoutine));
    } catch (e) {
      emit(ExamRoutineError(message: e.toString()));
    }
  }
}
