part of 'notices_bloc.dart';

@immutable
abstract class NoticesState extends Equatable {
  const NoticesState();

  @override
  List<Object> get props => [];
}

class NoticesInitial extends NoticesState {}

class NoticesLoading extends NoticesState {}

class NoticesLoaded extends NoticesState {
  final List<Notice> notices;

  const NoticesLoaded(this.notices);

  @override
  List<Object> get props => [notices];
}

class NoticesError extends NoticesState {
  final String message;

  const NoticesError(this.message);

  @override
  List<Object> get props => [message];
}