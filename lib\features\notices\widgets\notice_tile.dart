import 'package:flutter/material.dart';
import 'package:iubat_buddy/core/models/notice.dart'; // Assuming Notice model is in core/models

class NoticeTile extends StatelessWidget {
  final Notice notice;
  final VoidCallback? onTap;

  const NoticeTile({
    super.key,
    required this.notice,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 0),
      elevation: 2.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16.0),
        title: Text(
          notice.title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              notice.content, // Or a summary of content
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const SizedBox(height: 8),
            Text(
              // Format date nicely, e.g., using intl package
              'Published: ${notice.datePublished.toLocal().toString().split(' ')[0]}',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
        trailing: onTap != null ? const Icon(Icons.arrow_forward_ios, size: 16) : null,
        onTap: onTap,
      ),
    );
  }
}