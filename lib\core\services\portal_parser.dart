import 'package:iubat_buddy/core/models/course.dart';
import 'package:iubat_buddy/core/models/grade.dart';
import 'package:iubat_buddy/core/models/notice.dart';
import 'package:iubat_buddy/core/models/schedule_item.dart';
import 'package:iubat_buddy/core/models/user.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart'; // For TimeOfDay
import 'package:html/parser.dart' as html_parser; // For HTML parsing
import 'package:html/dom.dart' as dom; // For HTML DOM

abstract class PortalParser {
  Future<User> login({required String username, required String password}); // Changed to return User
  Future<List<Course>> getCourses();
  Future<List<Grade>> getGrades();
  Future<List<Notice>> getNotices();
  Future<List<ScheduleItem>> getTimetable();
  Future<User> getUserProfile(String url); // Added for checking auth status
  Future<User> getParticularInfo(); // Added for fetching detailed user information
  Future<void> logout(); // Added for actual logout
}


class IUBATParser extends PortalParser {
  final Dio dio;

  IUBATParser({required this.dio});

  @override
  Future<User> login({required String username, required String password}) async {
    const String loginUrl = 'https://iubat.online/Student/Home/login';
    const String dashboardUrl = 'https://iubat.online/Student/';
    print('IUBATParser: Attempting login for $username to $loginUrl');

    try {
      final response = await dio.post(
        loginUrl,
        data: {
          'txtstudent_userid': username,
          'txtstdpassword': password,
        },
        options: Options(
          contentType: Headers.formUrlEncodedContentType, // Important for form data
          followRedirects: false, // We want to inspect the 303 redirect ourselves
          validateStatus: (status) {
            return status != null && (status >= 200 && status < 300 || status == 303); // Allow 303
          },
        ),
      );

      if (response.statusCode == 303) {
        print('IUBATParser: Login successful, received 303 redirect.');
        // After successful login, fetch the dashboard page to get user info
        return await getUserProfile(dashboardUrl);
      } else if (response.statusCode == 200) {
        // This case might indicate a failed login if the page reloads with an error message
        // Further parsing of response.data (HTML) would be needed here to confirm.
        print('IUBATParser: Login request returned 200. Check response for error messages.');
        // For now, we'll assume it might be a failure if not a redirect.
        throw Exception('Login failed: Received status 200, expected redirect. Please check credentials or page content for error.');
      } else {
        print('IUBATParser: Login failed with status code: ${response.statusCode}');
        throw Exception('Login failed with status code: ${response.statusCode}');
      }
    } on DioException catch (e) {
      print('IUBATParser: DioError during login: ${e.message}');
      // Log more details if needed, e.g., e.response?.data
      throw Exception('Login failed: ${e.message}');
    } catch (e) {
      print('IUBATParser: Unknown error during login: $e');
      throw Exception('Login failed: An unknown error occurred.');
    }
  }

  @override
  Future<List<Course>> getCourses() async {
    print('IUBATParser: Fetching courses'); // Placeholder
    // Implement fetching and parsing logic for courses using dio
    // Example: final response = await dio.get('https://iubat.online/Student/Course');
    // Parse HTML or JSON from response.data
    await Future.delayed(const Duration(seconds: 1)); // Simulate network call
    return []; // Placeholder
    // throw UnimplementedError('getCourses not implemented yet');
  }

  static const Map<String, double> _gradeToGpaMap = {
    'A+': 4.00, 'A': 3.75, 'A-': 3.50,
    'B+': 3.25, 'B': 3.00, 'B-': 2.75,
    'C+': 2.50, 'C': 2.25, 'D': 2.00,
    'F': 0.00, 'I': 0.00,  // Assuming I (Incomplete) counts as 0 for GPA until resolved
    'W': 0.00   // Assuming W (Withdraw) counts as 0 for GPA and has 0 credit impact
    // Add any other grades if necessary
  };

  @override
  Future<List<Grade>> getGrades() async {
    const String gradesUrl = 'https://iubat.online/Student/Student-result';
    print('IUBATParser: Fetching grades from $gradesUrl');
    List<Grade> grades = [];

    try {
      final response = await dio.get(gradesUrl);
      if (response.statusCode == 200 && response.data != null) {
        final document = html_parser.parse(response.data);

        // Extract Semester Name
        String semesterName = 'N/A';
        // Updated selector for semester name based on the provided HTML structure
        final semesterPanelTitle = document.querySelector('div.panel-heading h3.panel-title');
        if (semesterPanelTitle != null) {
            final spanElement = semesterPanelTitle.querySelector('span');
            if (spanElement != null) {
                semesterName = spanElement.text.trim();
            } else {
                 // Fallback if span is not found, try to get text from h3 and clean it
                var h3Text = semesterPanelTitle.text.trim();
                if (h3Text.contains('Semester Result')) {
                    h3Text = h3Text.replaceFirst('Semester Result', '').trim();
                    // Further cleaning might be needed if there are other unwanted parts
                    semesterName = h3Text;
                } else {
                    print('IUBATParser: Semester name span not found, and h3 does not contain "Semester Result".');
                }
            }
        } else {
            print('IUBATParser: Could not find semester panel title element.');
        }

        // Find the table containing course information
        dom.Element? courseInfoTable;
        final panelTitles = document.querySelectorAll('.panel-heading .panel-title');
        for (var titleElement in panelTitles) {
            if (titleElement.text.trim() == 'Course Information') {
                final panelBody = titleElement.parent?.nextElementSibling;
                if (panelBody != null) {
                    courseInfoTable = panelBody.querySelector('table.table');
                    break;
                }
            }
        }

        if (courseInfoTable != null) {
            final courseRows = courseInfoTable.querySelectorAll('tbody > tr'); // Each course is in its own tbody > tr

            for (var row in courseRows) {
            final cells = row.querySelectorAll('td');
            if (cells.length == 5) { // #, Code, Title, Credit, Grade
                try {
                String courseCode = cells[1].text.trim();
                String courseName = cells[2].text.trim();
                double creditHours = double.tryParse(cells[3].text.trim()) ?? 0.0;
                String gradeValue = cells[4].text.trim();
                double? gpaPoints = _gradeToGpaMap[gradeValue.toUpperCase()];

                if (courseCode.isNotEmpty && courseName.isNotEmpty) {
                    grades.add(Grade(
                    semesterName: semesterName,
                    courseCode: courseCode,
                    courseName: courseName,
                    creditHours: creditHours,
                    gradeValue: gradeValue,
                    gpaPoints: gpaPoints,
                    ));
                }
                } catch (e) {
                print('IUBATParser: Error parsing a grade row: $e. Row HTML: ${row.outerHtml}');
                }
            } else if (cells.length >= 2 && cells.any((cell) => cell.text.contains('SGPA='))) {
                // This is the SGPA row, skip it for individual grades.
                print('IUBATParser: Found SGPA row: ${row.text.trim()}');
            }
            }
        } else {
            print('IUBATParser: Could not find the "Course Information" table.');
        }

        if (grades.isEmpty) {
            print('IUBATParser: No grades were parsed. Check selectors or page structure. Current semester: $semesterName');
        } else {
            print('IUBATParser: Parsed ${grades.length} grades for semester $semesterName.');
        }
        return grades;

      } else {
        print('IUBATParser: Failed to fetch grades HTML. Status: ${response.statusCode}');
        throw Exception('Failed to fetch grades HTML. Status: ${response.statusCode}');
      }
    } on DioException catch (e) {
      print('IUBATParser: DioError fetching grades: ${e.message}');
      throw Exception('Failed to fetch grades: ${e.message}');
    } catch (e) {
      print('IUBATParser: Error parsing grades: $e');
      throw Exception('Failed to parse grades: $e');
    }
  }

  @override
  Future<List<Notice>> getNotices() async {
    print('IUBATParser: Fetching notices'); // Placeholder
    await Future.delayed(const Duration(seconds: 1));
    return []; // Placeholder
    // throw UnimplementedError('getNotices not implemented yet');
  }

  TimeOfDay _parseTime(String timeStr) {
    // Expected format: "08:30am" or "01:10pm"
    final timePart = timeStr.substring(0, 5); // "08:30" or "01:10"
    final amPmPart = timeStr.substring(5).toLowerCase(); // "am" or "pm"

    List<String> parts = timePart.split(':');
    int hour = int.parse(parts[0]);
    int minute = int.parse(parts[1]);

    if (amPmPart == 'pm' && hour != 12) {
      hour += 12;
    } else if (amPmPart == 'am' && hour == 12) { // Midnight case (12am)
      hour = 0;
    }
    return TimeOfDay(hour: hour, minute: minute);
  }

  @override
  Future<List<ScheduleItem>> getTimetable() async {
    const String routineUrl = 'https://iubat.online/Student/Student-routine';
    print('IUBATParser: Fetching timetable from $routineUrl');
    List<ScheduleItem> scheduleItems = [];

    try {
      final response = await dio.get(routineUrl);
      if (response.statusCode == 200 && response.data != null) {
        final document = html_parser.parse(response.data);

        // Extract Semester Name (similar to grades page)
        String semesterName = 'N/A';
        final semesterPanelTitle = document.querySelector('div.panel-heading h3.panel-title');
        if (semesterPanelTitle != null) {
            final spanElement = semesterPanelTitle.querySelector('span');
            if (spanElement != null) {
                semesterName = spanElement.text.trim(); // e.g., "Spring 2025"
            }
        }
        print('IUBATParser: Parsing routine for semester: $semesterName');

        // Each class item is in a <div class="alert alert-default" id="abc">
        // which contains a <table class="table table-striped">
        final classDivs = document.querySelectorAll('div.panel-body div.alert.alert-default');

        for (var div in classDivs) {
          final table = div.querySelector('table.table-striped');
          if (table == null) continue;

          final rows = table.querySelectorAll('tbody > tr');
          if (rows.length < 2) continue; // Expecting header and data row

          final dataCells = rows[1].querySelectorAll('td'); // Data is in the second row
          if (dataCells.length == 3) {
            try {
              // 1st td: Course Code | Section, e.g., "CHM 117 (A)"
              String courseCodeWithSection = dataCells[0].text.trim();
              String courseCode = '';
              String section = '';
              final RegExp courseRegex = RegExp(r"([A-Z]{3}\s*\d{3})\s*\((.+)\)"); // Parses "CODE (SEC)"
              final courseMatch = courseRegex.firstMatch(courseCodeWithSection);
              if (courseMatch != null && courseMatch.groupCount >= 2) {
                courseCode = courseMatch.group(1)!.trim();
                section = courseMatch.group(2)!.trim();
              } else {
                // Fallback if regex fails, take the whole string as code and section as N/A
                courseCode = courseCodeWithSection;
                print('IUBATParser: Could not parse course code/section from "$courseCodeWithSection" using regex.');
              }

              // 2nd td: Day, e.g., "Saturday"
              String dayOfWeek = dataCells[1].text.trim();

              // 3rd td: Class Time and Room, e.g., "08:30am 09:30am <br> Room# 902"
              String timeAndRoomHtml = dataCells[2].innerHtml; // Use innerHtml to handle <br>
              String timeAndRoomText = dataCells[2].text.trim(); // Fallback or for easier splitting

              // Extract times: "08:30am 09:30am"
              final timeRegex = RegExp(r"(\d{2}:\d{2}[ap]m)\s*(\d{2}:\d{2}[ap]m)");
              final timeMatch = timeRegex.firstMatch(timeAndRoomText);
              if (timeMatch == null || timeMatch.groupCount < 2) {
                print('IUBATParser: Could not parse start/end time from "$timeAndRoomText"');
                continue;
              }
              TimeOfDay startTime = _parseTime(timeMatch.group(1)!);
              TimeOfDay endTime = _parseTime(timeMatch.group(2)!);

              // Extract Room: "Room# 902"
              String roomNumber = 'N/A';
              final roomRegex = RegExp(r"Room#\s*([\w\d-]+)"); // Allows alphanumeric and hyphen in room
              final roomMatch = roomRegex.firstMatch(timeAndRoomText); // timeAndRoomText should contain "Room# XXX"
              if (roomMatch != null && roomMatch.groupCount >= 1) {
                roomNumber = roomMatch.group(1)!.trim();
              } else {
                print('IUBATParser: Could not parse room number from "$timeAndRoomText"');
              }

              // Course name is not available here, instructor neither.
              scheduleItems.add(ScheduleItem(
                courseCode: courseCode,
                section: section,
                dayOfWeek: dayOfWeek,
                startTime: startTime,
                endTime: endTime,
                roomNumber: roomNumber,
                // courseName: null, // Explicitly null as not available
                // instructorName: null, // Explicitly null
              ));

            } catch (e, s) {
              print('IUBATParser: Error parsing a schedule item: $e. Stacktrace: $s. HTML: ${dataCells.map((c) => c.outerHtml).join()}');
            }
          }
        }

        if (scheduleItems.isEmpty && classDivs.isNotEmpty) {
            print('IUBATParser: Found class divs but failed to parse any schedule items.');
        } else if (scheduleItems.isEmpty) {
            print('IUBATParser: No class divs found or no schedule items parsed.');
        } else {
            print('IUBATParser: Parsed ${scheduleItems.length} schedule items for semester $semesterName.');
        }
        return scheduleItems;

      } else {
        print('IUBATParser: Failed to fetch routine HTML. Status: ${response.statusCode}');
        throw Exception('Failed to fetch routine HTML. Status: ${response.statusCode}');
      }
    } on DioException catch (e) {
      print('IUBATParser: DioError fetching routine: ${e.message}');
      throw Exception('Failed to fetch routine: ${e.message}');
    } catch (e, s) {
      print('IUBATParser: Error parsing routine: $e. Stacktrace: $s');
      throw Exception('Failed to parse routine: $e');
    }
  }

  @override
  Future<void> logout() async {
    const String logoutUrl = 'https://iubat.online/Student/logout';
    print('IUBATParser: Attempting to logout from $logoutUrl');
    try {
      // Logout is usually a GET request, or it might redirect.
      // We don't necessarily need to inspect the response too deeply,
      // just ensure the request is made.
      await dio.get(
        logoutUrl,
        options: Options(
          followRedirects: true, // Allow redirects
          validateStatus: (status) {
            // Allow success, redirects, or even if it lands on login page (common after logout)
            return status != null && (status >= 200 && status < 400);
          },
        ),
      );
      print('IUBATParser: Logout request to portal successful.');
      // The CookieManager should automatically handle cookie changes from the server response.
      // For PersistCookieJar, we might also want to explicitly clear.
    } on DioException catch (e) {
      // Even if logout request fails, we should proceed with local cleanup.
      print('IUBATParser: DioError during portal logout: ${e.message}. Proceeding with local cleanup.');
      // Optionally rethrow if this is critical, but for logout, local cleanup is often enough.
    } catch (e) {
      print('IUBATParser: Unknown error during portal logout: $e. Proceeding with local cleanup.');
    }
  }

  @override
  Future<User> getParticularInfo() async {
    const String particularUrl = 'https://iubat.online/Student/Student-particular';
    print('IUBATParser: Fetching particular information from $particularUrl');

    try {
      final response = await dio.get(particularUrl);
      if (response.statusCode == 200 && response.data != null) {
        final document = html_parser.parse(response.data);

        // Extract basic user information
        String studentId = '';
        String name = '';
        String program = '';
        String gender = '';
        String contactNo = '';
        String email = '';
        String cgpa = '';
        String fathersName = '';
        String guardianCell = '';
        String address = '';

        // Parse the table with student information
        final tableRows = document.querySelectorAll('table.table-striped tr');
        for (var row in tableRows) {
          final cells = row.querySelectorAll('td');
          if (cells.length >= 2) {
            final label = cells[0].text.trim();
            final value = cells[1].text.trim();

            if (label.contains('Name of Student')) {
              name = value;
            } else if (label.contains('Student ID')) {
              studentId = value;
            } else if (label.contains('Program')) {
              program = value;
            } else if (label.contains('Gender')) {
              gender = value;
            } else if (label.contains('Contact No')) {
              contactNo = value;
            } else if (label.contains('E-mail')) {
              email = value;
            } else if (label.contains('CGPA')) {
              cgpa = value;
            } else if (label.contains('Father')) {
              fathersName = value;
            } else if (label.contains('Guardian Cell')) {
              guardianCell = value;
            }
          }
        }

        // Extract address information
        final addressPanel = document.querySelector('.panel-body');
        if (addressPanel != null) {
          address = addressPanel.text.trim();
        }

        if (studentId.isNotEmpty && name.isNotEmpty) {
          print('IUBATParser: Particular info parsed successfully for $name (ID: $studentId)');
          return User(
            id: studentId,
            name: name,
            program: program,
            email: email,
            gender: gender,
            contactNo: contactNo,
            cgpa: cgpa,
            fathersName: fathersName,
            guardianCell: guardianCell,
            address: address,
          );
        } else {
          print('IUBATParser: Could not parse all user details from particular info page.');
          throw Exception('Failed to parse user details from particular info page.');
        }
      } else {
        print('IUBATParser: Failed to fetch particular info. Status: ${response.statusCode}');
        throw Exception('Failed to fetch particular info. Status: ${response.statusCode}');
      }
    } on DioException catch (e) {
      print('IUBATParser: DioError fetching particular info: ${e.message}');
      throw Exception('Failed to fetch particular info: ${e.message}');
    } catch (e) {
      print('IUBATParser: Error parsing particular info: $e');
      throw Exception('Failed to parse particular info: $e');
    }
  }

  @override
  Future<User> getUserProfile(String url) async {
    print('IUBATParser: Fetching user profile from $url');
    try {
      final response = await dio.get(url);
      if (response.statusCode == 200 && response.data != null) {
        final document = html_parser.parse(response.data);

        // Extract Student ID, Name, and Program from:
        // <div class="pull-left info"> <a href="#"><b>Student ID# 22103137 <br>Name: Hasin Esrak Mahe <br>Program: BCSE</b></a></div>
        String studentId = 'N/A';
        String name = 'N/A';
        String program = 'N/A';

        final userInfoElement = document.querySelector('aside.main-sidebar div.user-panel div.info a');
        if (userInfoElement != null) {
          final bElement = userInfoElement.querySelector('b');
          if (bElement != null) {
            final htmlContent = bElement.innerHtml; // Get inner HTML to handle <br>
            final parts = htmlContent.split('<br>').map((part) => part.trim()).toList();

            for (String part in parts) {
              if (part.startsWith('Student ID#')) {
                studentId = part.replaceFirst('Student ID#', '').trim();
              } else if (part.startsWith('Name:')) {
                name = part.replaceFirst('Name:', '').trim();
              } else if (part.startsWith('Program:')) {
                program = part.replaceFirst('Program:', '').trim();
              }
            }
          }
        }

        // Fallback for Student ID if not found in the sidebar user-panel, check the header dropdown
        if (studentId == 'N/A') {
            final headerIdElement = document.querySelector('header.main-header .user-menu .user-header small');
            if (headerIdElement != null && headerIdElement.text.contains('Student ID#')) {
                studentId = headerIdElement.text.replaceFirst('Student ID#', '').trim();
            }
        }


        if (studentId != 'N/A' && name != 'N/A') {
          print('IUBATParser: User profile parsed: ID: $studentId, Name: $name, Program: $program');
          return User(id: studentId, name: name, program: program);
        } else {
          print('IUBATParser: Could not parse all user details from dashboard.');
          throw Exception('Failed to parse user profile from dashboard HTML.');
        }

      } else {
        print('IUBATParser: Failed to fetch dashboard HTML. Status: ${response.statusCode}');
        throw Exception('Failed to fetch dashboard HTML. Status: ${response.statusCode}');
      }
    } on DioException catch (e) {
      print('IUBATParser: DioError fetching user profile: ${e.message}');
      throw Exception('Failed to fetch user profile: ${e.message}');
    } catch (e) {
      print('IUBATParser: Error parsing user profile: $e');
      throw Exception('Failed to parse user profile: $e');
    }
  }
}