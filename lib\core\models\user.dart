class User {
  final String id;         // Student ID
  final String name;       // Full name
  final String? program;   // Program/Department
  final String? profileImageUrl;
  final String? email;     // Student email
  final String? batch;     // Batch number
  final String? semester;  // Current semester
  final String? studentId; // Same as id, but kept for compatibility

  // Additional fields from particular information
  final String? gender;
  final String? contactNo;
  final String? cgpa;
  final String? fathersName;
  final String? guardianCell;
  final String? address;   // Full address or village/upazilla/post-office/district

  const User({
    required this.id,
    required this.name,
    this.program,
    this.profileImageUrl,
    this.email,
    this.batch,
    this.semester,
    this.studentId,
    this.gender,
    this.contactNo,
    this.cgpa,
    this.fathersName,
    this.guardianCell,
    this.address,
  });

  // Create a copy of this User with the given field values updated
  User copyWith({
    String? id,
    String? name,
    String? program,
    String? profileImageUrl,
    String? email,
    String? batch,
    String? semester,
    String? studentId,
    String? gender,
    String? contactNo,
    String? cgpa,
    String? fathersName,
    String? guardianCell,
    String? address,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      program: program ?? this.program,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      email: email ?? this.email,
      batch: batch ?? this.batch,
      semester: semester ?? this.semester,
      studentId: studentId ?? this.studentId,
      gender: gender ?? this.gender,
      contactNo: contactNo ?? this.contactNo,
      cgpa: cgpa ?? this.cgpa,
      fathersName: fathersName ?? this.fathersName,
      guardianCell: guardianCell ?? this.guardianCell,
      address: address ?? this.address,
    );
  }

  // factory User.fromJson(Map<String, dynamic> json) {
  //   return User(
  //     id: json['id'],
  //     name: json['name'],
  //     program: json['program'],
  //     profileImageUrl: json['profileImageUrl'],
  //     email: json['email'],
  //     batch: json['batch'],
  //     semester: json['semester'],
  //     studentId: json['studentId'] ?? json['id'],
  //   );
  // }

  // Map<String, dynamic> toJson() {
  //   return {
  //     'id': id,
  //     'name': name,
  //     'program': program,
  //     'profileImageUrl': profileImageUrl,
  //     'email': email,
  //     'batch': batch,
  //     'semester': semester,
  //     'studentId': studentId ?? id,
  //   };
  // }
}