import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:iubat_buddy/features/auth/bloc/auth_bloc.dart';
// auth_event.dart is part of auth_bloc.dart, so this direct import is not needed and causes errors.
import 'package:iubat_buddy/features/grades/ui/grades_screen.dart';
import 'package:iubat_buddy/features/notices/ui/notices_screen.dart';
import 'package:iubat_buddy/features/schedule/ui/schedule_screen.dart';
// Import other feature Blocs (CoursesBloc, GradesBloc, etc.) and their states/events when created

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              context.read<AuthBloc>().add(AuthLogoutRequested());
            },
          )
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: CustomScrollView(
          slivers: <Widget>[
            // User Info / Welcome message section removed.
            // SliverGrid for the dashboard cards
            SliverPadding(
              padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0), // Adjusted padding
              sliver: SliverGrid(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2, // Number of columns
                  crossAxisSpacing: 16.0, // Horizontal space between cards
                  mainAxisSpacing: 16.0, // Vertical space between cards
                  childAspectRatio: 1.2, // Aspect ratio of the cards (width / height)
                ),
                delegate: SliverChildListDelegate(
                  [
                    // Mapping portal items to our cards
                    _buildDashboardCard( // Was "My Grades"
                      context,
                      title: 'Semester Result',
                      icon: Icons.assessment, // Kept assessment icon
                      onTap: () {
                        Navigator.push(context, MaterialPageRoute(builder: (_) => const GradesScreen()));
                      },
                    ),
                    _buildDashboardCard( // Was "Class Schedule"
                      context,
                      title: 'Class Routine',
                      icon: Icons.schedule, // Kept schedule icon
                      onTap: () {
                        Navigator.push(context, MaterialPageRoute(builder: (_) => const ScheduleScreen()));
                      },
                    ),
                     _buildDashboardCard( // New: Particular Information
                      context,
                      title: 'Particular Info',
                      icon: Icons.person_search, // Example icon
                      onTap: () {
                        // TODO: Navigate to ParticularInformationScreen or show message
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Particular Information: Not yet implemented'))
                        );
                      },
                    ),
                    _buildDashboardCard( // New: Exam Routine
                      context,
                      title: 'Exam Routine',
                      icon: Icons.event_note, // Example icon
                      onTap: () {
                        // TODO: Navigate to ExamRoutineScreen or show message
                         ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Exam Routine: Not yet implemented'))
                        );
                      },
                    ),
                    _buildDashboardCard( // New: Account Information (External)
                      context,
                      title: 'Account Info',
                      icon: Icons.account_balance_wallet, // Example icon
                      onTap: () async {
                        // TODO: Implement opening external link (use url_launcher package)
                        // For now, show a message
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Account Info: External link not yet implemented'))
                        );
                        // Example with url_launcher (add package first):
                        // final Uri url = Uri.parse('https://iubatpayment.net/');
                        // if (!await launchUrl(url)) {
                        //   ScaffoldMessenger.of(context).showSnackBar(
                        //     SnackBar(content: Text('Could not launch $url'))
                        //   );
                        // }
                      },
                    ),
                    _buildDashboardCard( // New: Bus Schedule
                      context,
                      title: 'Bus Schedule',
                      icon: Icons.directions_bus, // Example icon
                      onTap: () {
                        // TODO: Navigate to BusScheduleScreen or show message
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Bus Schedule: Not yet implemented'))
                        );
                      },
                    ),
                    _buildDashboardCard( // New: CIS
                      context,
                      title: 'CIS Survey',
                      icon: Icons.rate_review, // Example icon
                      onTap: () {
                        // TODO: Navigate to CISScreen or show message
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('CIS Survey: Not yet implemented'))
                        );
                      },
                    ),
                    _buildDashboardCard( // Kept "Notices"
                      context,
                      title: 'Notices',
                      icon: Icons.campaign,
                      onTap: () {
                        Navigator.push(context, MaterialPageRoute(builder: (_) => const NoticesScreen()));
                      },
                    ),
                    // Optional: My Courses - decide if needed
                    // _buildDashboardCard(
                    //   context,
                    //   title: 'My Courses',
                    //   icon: Icons.school,
                    //   onTap: () {
                    //     Navigator.push(context, MaterialPageRoute(builder: (_) => const CoursesScreen()));
                    //   },
                    // ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      // Placeholder for BottomNavigationBar as per app-data.md
      // bottomNavigationBar: BottomNavigationBar(...),
    );
  }

  Widget _buildDashboardCard(BuildContext context, {required String title, required IconData icon, VoidCallback? onTap}) {
    return Card(
      elevation: 4.0, // Subtle shadow
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0), // Rounded corners
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16.0),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Icon(icon, size: 40, color: Theme.of(context).primaryColor),
              const SizedBox(height: 10),
              Text(title, style: Theme.of(context).textTheme.titleMedium),
              // Add data summaries here later
            ],
          ),
        ),
      ),
    );
  }
}