import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:iubat_buddy/core/models/grade.dart';
import 'package:iubat_buddy/core/repositories/iubat_repository.dart';
import 'package:meta/meta.dart';

part 'grades_event.dart';
part 'grades_state.dart';

class GradesBloc extends Bloc<GradesEvent, GradesState> {
  final IubatRepository iubatRepository;

  GradesBloc({required this.iubatRepository}) : super(GradesInitial()) {
    on<LoadGrades>(_onLoadGrades);
  }

  Future<void> _onLoadGrades(LoadGrades event, Emitter<GradesState> emit) async {
    emit(GradesLoading());
    try {
      final grades = await iubatRepository.getGrades();
      final double cgpa = _calculateCGPA(grades);
      emit(GradesLoaded(grades, cgpa));
    } catch (e) {
      emit(GradesError(e.toString()));
    }
  }

  double _calculateCGPA(List<Grade> grades) {
    if (grades.isEmpty) return 0.0;

    double totalWeightedGpaPoints = 0;
    double totalCreditHoursAttempted = 0;

    for (var grade in grades) {
      if (grade.gpaPoints != null && grade.creditHours > 0) {
        // Only include courses with valid GPA points and credit hours in CGPA calculation
        // Some grading systems might exclude 'F' grades from total credits for CGPA once retaken,
        // or handle 'W' (Withdraw) or 'I' (Incomplete) differently.
        // For now, we assume all fetched grades with gpaPoints contribute.
        totalWeightedGpaPoints += (grade.gpaPoints! * grade.creditHours);
        totalCreditHoursAttempted += grade.creditHours;
      }
    }

    if (totalCreditHoursAttempted == 0) return 0.0;

    return totalWeightedGpaPoints / totalCreditHoursAttempted;
  }
}