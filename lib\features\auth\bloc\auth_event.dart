part of 'auth_bloc.dart';

@immutable
abstract class AuthEvent extends Equatable { // Added Equatable
  const AuthEvent(); // Added const constructor

  @override
  List<Object> get props => []; // Default props for Equatable
}

class AuthLoginRequested extends AuthEvent {
  final String username;
  final String password;

  const AuthLoginRequested({required this.username, required this.password}); // Made const

  @override
  List<Object> get props => [username, password]; // Added props
}

class AuthLogoutRequested extends AuthEvent {
  const AuthLogoutRequested(); // Made const
}

class AuthCheckStatusRequested extends AuthEvent { // New event
  const AuthCheckStatusRequested(); // Made const
}