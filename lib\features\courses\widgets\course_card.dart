import 'package:flutter/material.dart';
import 'package:iubat_buddy/core/models/course.dart'; // Assuming Course model

class CourseCard extends StatelessWidget {
  final Course course;
  final VoidCallback? onTap;

  const CourseCard({
    super.key,
    required this.course,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 3.0,
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12.0),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                course.title,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold, color: Theme.of(context).primaryColorDark),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Text(
                'Course Code: ${course.code}',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              const SizedBox(height: 4),
              // Add more details if available in the Course model, e.g., credits, instructor
              // Text('Credits: ${course.credits}', style: Theme.of(context).textTheme.bodyMedium),
              if (onTap != null)
                Align(
                  alignment: Alignment.centerRight,
                  child: Icon(Icons.arrow_forward_ios, size: 16, color: Theme.of(context).primaryColor),
                )
            ],
          ),
        ),
      ),
    );
  }
}